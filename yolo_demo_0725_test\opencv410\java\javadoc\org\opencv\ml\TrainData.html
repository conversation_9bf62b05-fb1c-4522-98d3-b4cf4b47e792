<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_392) on Sun Jun 02 16:58:26 UTC 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>TrainData (OpenCV 4.10.0 Java documentation)</title>
<meta name="date" content="2024-06-02">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="TrainData (OpenCV 4.10.0 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":9,"i27":9,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10,"i41":10,"i42":10,"i43":10,"i44":10,"i45":10,"i46":10,"i47":10,"i48":10,"i49":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/ml/SVMSGD.html" title="class in org.opencv.ml"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/ml/TrainData.html" target="_top">Frames</a></li>
<li><a href="TrainData.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.ml</div>
<h2 title="Class TrainData" class="title">Class TrainData</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.opencv.ml.TrainData</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">TrainData</span>
extends java.lang.Object</pre>
<div class="block">Class encapsulating training data.

 Please note that the class only specifies the interface of training data, but not implementation.
 All the statistical model classes in _ml_ module accepts Ptr&lt;TrainData&gt; as parameter. In other
 words, you can create your own class derived from TrainData and pass smart pointer to the instance
 of this class into StatModel::train.

 SEE: REF: ml_intro_data</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/ml/TrainData.html" title="class in org.opencv.ml">TrainData</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/TrainData.html#Z:Z__fromPtr__-long-">__fromPtr__</a></span>(long&nbsp;addr)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/ml/TrainData.html" title="class in org.opencv.ml">TrainData</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/TrainData.html#create-org.opencv.core.Mat-int-org.opencv.core.Mat-">create</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
      int&nbsp;layout,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;responses)</code>
<div class="block">Creates training data from in-memory arrays.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/ml/TrainData.html" title="class in org.opencv.ml">TrainData</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/TrainData.html#create-org.opencv.core.Mat-int-org.opencv.core.Mat-org.opencv.core.Mat-">create</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
      int&nbsp;layout,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;responses,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;varIdx)</code>
<div class="block">Creates training data from in-memory arrays.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/ml/TrainData.html" title="class in org.opencv.ml">TrainData</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/TrainData.html#create-org.opencv.core.Mat-int-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">create</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
      int&nbsp;layout,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;responses,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;varIdx,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;sampleIdx)</code>
<div class="block">Creates training data from in-memory arrays.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/ml/TrainData.html" title="class in org.opencv.ml">TrainData</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/TrainData.html#create-org.opencv.core.Mat-int-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">create</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
      int&nbsp;layout,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;responses,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;varIdx,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;sampleIdx,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;sampleWeights)</code>
<div class="block">Creates training data from in-memory arrays.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/ml/TrainData.html" title="class in org.opencv.ml">TrainData</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/TrainData.html#create-org.opencv.core.Mat-int-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">create</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
      int&nbsp;layout,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;responses,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;varIdx,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;sampleIdx,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;sampleWeights,
      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;varType)</code>
<div class="block">Creates training data from in-memory arrays.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/TrainData.html#getCatCount-int-">getCatCount</a></span>(int&nbsp;vi)</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/TrainData.html#getCatMap--">getCatMap</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/TrainData.html#getCatOfs--">getCatOfs</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/TrainData.html#getClassLabels--">getClassLabels</a></span>()</code>
<div class="block">Returns the vector of class labels

     The function returns vector of unique labels occurred in the responses.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/TrainData.html#getDefaultSubstValues--">getDefaultSubstValues</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/TrainData.html#getLayout--">getLayout</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/TrainData.html#getMissing--">getMissing</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/TrainData.html#getNAllVars--">getNAllVars</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/TrainData.html#getNames-java.util.List-">getNames</a></span>(java.util.List&lt;java.lang.String&gt;&nbsp;names)</code>
<div class="block">Returns vector of symbolic names captured in loadFromCSV()</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/TrainData.html#getNativeObjAddr--">getNativeObjAddr</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/TrainData.html#getNormCatResponses--">getNormCatResponses</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/TrainData.html#getNSamples--">getNSamples</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/TrainData.html#getNTestSamples--">getNTestSamples</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/TrainData.html#getNTrainSamples--">getNTrainSamples</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/TrainData.html#getNVars--">getNVars</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/TrainData.html#getResponses--">getResponses</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/TrainData.html#getResponseType--">getResponseType</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/TrainData.html#getSample-org.opencv.core.Mat-int-float-">getSample</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;varIdx,
         int&nbsp;sidx,
         float&nbsp;buf)</code>&nbsp;</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/TrainData.html#getSamples--">getSamples</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/TrainData.html#getSampleWeights--">getSampleWeights</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/TrainData.html#getSubMatrix-org.opencv.core.Mat-org.opencv.core.Mat-int-">getSubMatrix</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;matrix,
            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;idx,
            int&nbsp;layout)</code>
<div class="block">Extract from matrix rows/cols specified by passed indexes.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/TrainData.html#getSubVector-org.opencv.core.Mat-org.opencv.core.Mat-">getSubVector</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;vec,
            <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;idx)</code>
<div class="block">Extract from 1D vector elements specified by passed indexes.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/TrainData.html#getTestNormCatResponses--">getTestNormCatResponses</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/TrainData.html#getTestResponses--">getTestResponses</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/TrainData.html#getTestSampleIdx--">getTestSampleIdx</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/TrainData.html#getTestSamples--">getTestSamples</a></span>()</code>
<div class="block">Returns matrix of test samples</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/TrainData.html#getTestSampleWeights--">getTestSampleWeights</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/TrainData.html#getTrainNormCatResponses--">getTrainNormCatResponses</a></span>()</code>
<div class="block">Returns the vector of normalized categorical responses

     The function returns vector of responses.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/TrainData.html#getTrainResponses--">getTrainResponses</a></span>()</code>
<div class="block">Returns the vector of responses

     The function returns ordered or the original categorical responses.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/TrainData.html#getTrainSampleIdx--">getTrainSampleIdx</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/TrainData.html#getTrainSamples--">getTrainSamples</a></span>()</code>
<div class="block">Returns matrix of train samples

         transposed.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/TrainData.html#getTrainSamples-int-">getTrainSamples</a></span>(int&nbsp;layout)</code>
<div class="block">Returns matrix of train samples</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/TrainData.html#getTrainSamples-int-boolean-">getTrainSamples</a></span>(int&nbsp;layout,
               boolean&nbsp;compressSamples)</code>
<div class="block">Returns matrix of train samples</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/TrainData.html#getTrainSamples-int-boolean-boolean-">getTrainSamples</a></span>(int&nbsp;layout,
               boolean&nbsp;compressSamples,
               boolean&nbsp;compressVars)</code>
<div class="block">Returns matrix of train samples</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/TrainData.html#getTrainSampleWeights--">getTrainSampleWeights</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/TrainData.html#getValues-int-org.opencv.core.Mat-float-">getValues</a></span>(int&nbsp;vi,
         <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;sidx,
         float&nbsp;values)</code>&nbsp;</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/TrainData.html#getVarIdx--">getVarIdx</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/TrainData.html#getVarSymbolFlags--">getVarSymbolFlags</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/TrainData.html#getVarType--">getVarType</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/TrainData.html#setTrainTestSplit-int-">setTrainTestSplit</a></span>(int&nbsp;count)</code>
<div class="block">Splits the training data into the training and test parts
     SEE: TrainData::setTrainTestSplitRatio</div>
</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/TrainData.html#setTrainTestSplit-int-boolean-">setTrainTestSplit</a></span>(int&nbsp;count,
                 boolean&nbsp;shuffle)</code>
<div class="block">Splits the training data into the training and test parts
     SEE: TrainData::setTrainTestSplitRatio</div>
</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/TrainData.html#setTrainTestSplitRatio-double-">setTrainTestSplitRatio</a></span>(double&nbsp;ratio)</code>
<div class="block">Splits the training data into the training and test parts

     The function selects a subset of specified relative size and then returns it as the training
     set.</div>
</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/TrainData.html#setTrainTestSplitRatio-double-boolean-">setTrainTestSplitRatio</a></span>(double&nbsp;ratio,
                      boolean&nbsp;shuffle)</code>
<div class="block">Splits the training data into the training and test parts

     The function selects a subset of specified relative size and then returns it as the training
     set.</div>
</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/ml/TrainData.html#shuffleTrainTest--">shuffleTrainTest</a></span>()</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="Z:Z__fromPtr__-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>__fromPtr__</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/ml/TrainData.html" title="class in org.opencv.ml">TrainData</a>&nbsp;__fromPtr__(long&nbsp;addr)</pre>
</li>
</ul>
<a name="create-org.opencv.core.Mat-int-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/ml/TrainData.html" title="class in org.opencv.ml">TrainData</a>&nbsp;create(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
                               int&nbsp;layout,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;responses)</pre>
<div class="block">Creates training data from in-memory arrays.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>samples</code> - matrix of samples. It should have CV_32F type.</dd>
<dd><code>layout</code> - see ml::SampleTypes.</dd>
<dd><code>responses</code> - matrix of responses. If the responses are scalar, they should be stored as a
         single row or as a single column. The matrix should have type CV_32F or CV_32S (in the
         former case the responses are considered as ordered by default; in the latter case - as
         categorical)
         (CV_32S) containing 0-based variable indices or byte vector (CV_8U) containing a mask of
         active variables.
         vector (CV_32S) containing 0-based sample indices or byte vector (CV_8U) containing a mask
         of training samples.
         &lt;number_of_variables_in_responses&gt;`, containing types of each input and output variable. See
         ml::VariableTypes.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-org.opencv.core.Mat-int-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/ml/TrainData.html" title="class in org.opencv.ml">TrainData</a>&nbsp;create(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
                               int&nbsp;layout,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;responses,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;varIdx)</pre>
<div class="block">Creates training data from in-memory arrays.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>samples</code> - matrix of samples. It should have CV_32F type.</dd>
<dd><code>layout</code> - see ml::SampleTypes.</dd>
<dd><code>responses</code> - matrix of responses. If the responses are scalar, they should be stored as a
         single row or as a single column. The matrix should have type CV_32F or CV_32S (in the
         former case the responses are considered as ordered by default; in the latter case - as
         categorical)</dd>
<dd><code>varIdx</code> - vector specifying which variables to use for training. It can be an integer vector
         (CV_32S) containing 0-based variable indices or byte vector (CV_8U) containing a mask of
         active variables.
         vector (CV_32S) containing 0-based sample indices or byte vector (CV_8U) containing a mask
         of training samples.
         &lt;number_of_variables_in_responses&gt;`, containing types of each input and output variable. See
         ml::VariableTypes.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-org.opencv.core.Mat-int-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/ml/TrainData.html" title="class in org.opencv.ml">TrainData</a>&nbsp;create(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
                               int&nbsp;layout,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;responses,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;varIdx,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;sampleIdx)</pre>
<div class="block">Creates training data from in-memory arrays.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>samples</code> - matrix of samples. It should have CV_32F type.</dd>
<dd><code>layout</code> - see ml::SampleTypes.</dd>
<dd><code>responses</code> - matrix of responses. If the responses are scalar, they should be stored as a
         single row or as a single column. The matrix should have type CV_32F or CV_32S (in the
         former case the responses are considered as ordered by default; in the latter case - as
         categorical)</dd>
<dd><code>varIdx</code> - vector specifying which variables to use for training. It can be an integer vector
         (CV_32S) containing 0-based variable indices or byte vector (CV_8U) containing a mask of
         active variables.</dd>
<dd><code>sampleIdx</code> - vector specifying which samples to use for training. It can be an integer
         vector (CV_32S) containing 0-based sample indices or byte vector (CV_8U) containing a mask
         of training samples.
         &lt;number_of_variables_in_responses&gt;`, containing types of each input and output variable. See
         ml::VariableTypes.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-org.opencv.core.Mat-int-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/ml/TrainData.html" title="class in org.opencv.ml">TrainData</a>&nbsp;create(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
                               int&nbsp;layout,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;responses,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;varIdx,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;sampleIdx,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;sampleWeights)</pre>
<div class="block">Creates training data from in-memory arrays.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>samples</code> - matrix of samples. It should have CV_32F type.</dd>
<dd><code>layout</code> - see ml::SampleTypes.</dd>
<dd><code>responses</code> - matrix of responses. If the responses are scalar, they should be stored as a
         single row or as a single column. The matrix should have type CV_32F or CV_32S (in the
         former case the responses are considered as ordered by default; in the latter case - as
         categorical)</dd>
<dd><code>varIdx</code> - vector specifying which variables to use for training. It can be an integer vector
         (CV_32S) containing 0-based variable indices or byte vector (CV_8U) containing a mask of
         active variables.</dd>
<dd><code>sampleIdx</code> - vector specifying which samples to use for training. It can be an integer
         vector (CV_32S) containing 0-based sample indices or byte vector (CV_8U) containing a mask
         of training samples.</dd>
<dd><code>sampleWeights</code> - optional vector with weights for each sample. It should have CV_32F type.
         &lt;number_of_variables_in_responses&gt;`, containing types of each input and output variable. See
         ml::VariableTypes.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-org.opencv.core.Mat-int-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/ml/TrainData.html" title="class in org.opencv.ml">TrainData</a>&nbsp;create(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;samples,
                               int&nbsp;layout,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;responses,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;varIdx,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;sampleIdx,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;sampleWeights,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;varType)</pre>
<div class="block">Creates training data from in-memory arrays.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>samples</code> - matrix of samples. It should have CV_32F type.</dd>
<dd><code>layout</code> - see ml::SampleTypes.</dd>
<dd><code>responses</code> - matrix of responses. If the responses are scalar, they should be stored as a
         single row or as a single column. The matrix should have type CV_32F or CV_32S (in the
         former case the responses are considered as ordered by default; in the latter case - as
         categorical)</dd>
<dd><code>varIdx</code> - vector specifying which variables to use for training. It can be an integer vector
         (CV_32S) containing 0-based variable indices or byte vector (CV_8U) containing a mask of
         active variables.</dd>
<dd><code>sampleIdx</code> - vector specifying which samples to use for training. It can be an integer
         vector (CV_32S) containing 0-based sample indices or byte vector (CV_8U) containing a mask
         of training samples.</dd>
<dd><code>sampleWeights</code> - optional vector with weights for each sample. It should have CV_32F type.</dd>
<dd><code>varType</code> - optional vector of type CV_8U and size `&lt;number_of_variables_in_samples&gt; +
         &lt;number_of_variables_in_responses&gt;`, containing types of each input and output variable. See
         ml::VariableTypes.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getCatCount-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCatCount</h4>
<pre>public&nbsp;int&nbsp;getCatCount(int&nbsp;vi)</pre>
</li>
</ul>
<a name="getCatMap--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCatMap</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;getCatMap()</pre>
</li>
</ul>
<a name="getCatOfs--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCatOfs</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;getCatOfs()</pre>
</li>
</ul>
<a name="getClassLabels--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getClassLabels</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;getClassLabels()</pre>
<div class="block">Returns the vector of class labels

     The function returns vector of unique labels occurred in the responses.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getDefaultSubstValues--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDefaultSubstValues</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;getDefaultSubstValues()</pre>
</li>
</ul>
<a name="getLayout--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLayout</h4>
<pre>public&nbsp;int&nbsp;getLayout()</pre>
</li>
</ul>
<a name="getMissing--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMissing</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;getMissing()</pre>
</li>
</ul>
<a name="getNAllVars--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNAllVars</h4>
<pre>public&nbsp;int&nbsp;getNAllVars()</pre>
</li>
</ul>
<a name="getNames-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNames</h4>
<pre>public&nbsp;void&nbsp;getNames(java.util.List&lt;java.lang.String&gt;&nbsp;names)</pre>
<div class="block">Returns vector of symbolic names captured in loadFromCSV()</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>names</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="getNativeObjAddr--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNativeObjAddr</h4>
<pre>public&nbsp;long&nbsp;getNativeObjAddr()</pre>
</li>
</ul>
<a name="getNormCatResponses--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNormCatResponses</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;getNormCatResponses()</pre>
</li>
</ul>
<a name="getNSamples--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNSamples</h4>
<pre>public&nbsp;int&nbsp;getNSamples()</pre>
</li>
</ul>
<a name="getNTestSamples--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNTestSamples</h4>
<pre>public&nbsp;int&nbsp;getNTestSamples()</pre>
</li>
</ul>
<a name="getNTrainSamples--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNTrainSamples</h4>
<pre>public&nbsp;int&nbsp;getNTrainSamples()</pre>
</li>
</ul>
<a name="getNVars--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNVars</h4>
<pre>public&nbsp;int&nbsp;getNVars()</pre>
</li>
</ul>
<a name="getResponses--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResponses</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;getResponses()</pre>
</li>
</ul>
<a name="getResponseType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResponseType</h4>
<pre>public&nbsp;int&nbsp;getResponseType()</pre>
</li>
</ul>
<a name="getSample-org.opencv.core.Mat-int-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSample</h4>
<pre>public&nbsp;void&nbsp;getSample(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;varIdx,
                      int&nbsp;sidx,
                      float&nbsp;buf)</pre>
</li>
</ul>
<a name="getSamples--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSamples</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;getSamples()</pre>
</li>
</ul>
<a name="getSampleWeights--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSampleWeights</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;getSampleWeights()</pre>
</li>
</ul>
<a name="getSubMatrix-org.opencv.core.Mat-org.opencv.core.Mat-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSubMatrix</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;getSubMatrix(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;matrix,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;idx,
                               int&nbsp;layout)</pre>
<div class="block">Extract from matrix rows/cols specified by passed indexes.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>matrix</code> - input matrix (supported types: CV_32S, CV_32F, CV_64F)</dd>
<dd><code>idx</code> - 1D index vector</dd>
<dd><code>layout</code> - specifies to extract rows (cv::ml::ROW_SAMPLES) or to extract columns (cv::ml::COL_SAMPLES)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getSubVector-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSubVector</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;getSubVector(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;vec,
                               <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;idx)</pre>
<div class="block">Extract from 1D vector elements specified by passed indexes.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>vec</code> - input vector (supported types: CV_32S, CV_32F, CV_64F)</dd>
<dd><code>idx</code> - 1D index vector</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getTestNormCatResponses--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTestNormCatResponses</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;getTestNormCatResponses()</pre>
</li>
</ul>
<a name="getTestResponses--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTestResponses</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;getTestResponses()</pre>
</li>
</ul>
<a name="getTestSampleIdx--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTestSampleIdx</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;getTestSampleIdx()</pre>
</li>
</ul>
<a name="getTestSamples--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTestSamples</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;getTestSamples()</pre>
<div class="block">Returns matrix of test samples</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getTestSampleWeights--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTestSampleWeights</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;getTestSampleWeights()</pre>
</li>
</ul>
<a name="getTrainNormCatResponses--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTrainNormCatResponses</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;getTrainNormCatResponses()</pre>
<div class="block">Returns the vector of normalized categorical responses

     The function returns vector of responses. Each response is integer from <code>0</code> to `&lt;number of
     classes&gt;-1`. The actual label value can be retrieved then from the class label vector, see
     TrainData::getClassLabels.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getTrainResponses--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTrainResponses</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;getTrainResponses()</pre>
<div class="block">Returns the vector of responses

     The function returns ordered or the original categorical responses. Usually it's used in
     regression algorithms.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getTrainSampleIdx--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTrainSampleIdx</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;getTrainSampleIdx()</pre>
</li>
</ul>
<a name="getTrainSamples--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTrainSamples</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;getTrainSamples()</pre>
<div class="block">Returns matrix of train samples

         transposed. See ml::SampleTypes.
         sampleIdx)
         the active variables.

     In current implementation the function tries to avoid physical data copying and returns the
     matrix stored inside TrainData (unless the transposition or compression is needed).</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getTrainSamples-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTrainSamples</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;getTrainSamples(int&nbsp;layout)</pre>
<div class="block">Returns matrix of train samples</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>layout</code> - The requested layout. If it's different from the initial one, the matrix is
         transposed. See ml::SampleTypes.
         sampleIdx)
         the active variables.

     In current implementation the function tries to avoid physical data copying and returns the
     matrix stored inside TrainData (unless the transposition or compression is needed).</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getTrainSamples-int-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTrainSamples</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;getTrainSamples(int&nbsp;layout,
                           boolean&nbsp;compressSamples)</pre>
<div class="block">Returns matrix of train samples</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>layout</code> - The requested layout. If it's different from the initial one, the matrix is
         transposed. See ml::SampleTypes.</dd>
<dd><code>compressSamples</code> - if true, the function returns only the training samples (specified by
         sampleIdx)
         the active variables.

     In current implementation the function tries to avoid physical data copying and returns the
     matrix stored inside TrainData (unless the transposition or compression is needed).</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getTrainSamples-int-boolean-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTrainSamples</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;getTrainSamples(int&nbsp;layout,
                           boolean&nbsp;compressSamples,
                           boolean&nbsp;compressVars)</pre>
<div class="block">Returns matrix of train samples</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>layout</code> - The requested layout. If it's different from the initial one, the matrix is
         transposed. See ml::SampleTypes.</dd>
<dd><code>compressSamples</code> - if true, the function returns only the training samples (specified by
         sampleIdx)</dd>
<dd><code>compressVars</code> - if true, the function returns the shorter training samples, containing only
         the active variables.

     In current implementation the function tries to avoid physical data copying and returns the
     matrix stored inside TrainData (unless the transposition or compression is needed).</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getTrainSampleWeights--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTrainSampleWeights</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;getTrainSampleWeights()</pre>
</li>
</ul>
<a name="getValues-int-org.opencv.core.Mat-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getValues</h4>
<pre>public&nbsp;void&nbsp;getValues(int&nbsp;vi,
                      <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;sidx,
                      float&nbsp;values)</pre>
</li>
</ul>
<a name="getVarIdx--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVarIdx</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;getVarIdx()</pre>
</li>
</ul>
<a name="getVarSymbolFlags--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVarSymbolFlags</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;getVarSymbolFlags()</pre>
</li>
</ul>
<a name="getVarType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVarType</h4>
<pre>public&nbsp;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;getVarType()</pre>
</li>
</ul>
<a name="setTrainTestSplit-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTrainTestSplit</h4>
<pre>public&nbsp;void&nbsp;setTrainTestSplit(int&nbsp;count)</pre>
<div class="block">Splits the training data into the training and test parts
     SEE: TrainData::setTrainTestSplitRatio</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>count</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setTrainTestSplit-int-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTrainTestSplit</h4>
<pre>public&nbsp;void&nbsp;setTrainTestSplit(int&nbsp;count,
                              boolean&nbsp;shuffle)</pre>
<div class="block">Splits the training data into the training and test parts
     SEE: TrainData::setTrainTestSplitRatio</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>count</code> - automatically generated</dd>
<dd><code>shuffle</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setTrainTestSplitRatio-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTrainTestSplitRatio</h4>
<pre>public&nbsp;void&nbsp;setTrainTestSplitRatio(double&nbsp;ratio)</pre>
<div class="block">Splits the training data into the training and test parts

     The function selects a subset of specified relative size and then returns it as the training
     set. If the function is not called, all the data is used for training. Please, note that for
     each of TrainData::getTrain\* there is corresponding TrainData::getTest\*, so that the test
     subset can be retrieved and processed as well.
     SEE: TrainData::setTrainTestSplit</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>ratio</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="setTrainTestSplitRatio-double-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTrainTestSplitRatio</h4>
<pre>public&nbsp;void&nbsp;setTrainTestSplitRatio(double&nbsp;ratio,
                                   boolean&nbsp;shuffle)</pre>
<div class="block">Splits the training data into the training and test parts

     The function selects a subset of specified relative size and then returns it as the training
     set. If the function is not called, all the data is used for training. Please, note that for
     each of TrainData::getTrain\* there is corresponding TrainData::getTest\*, so that the test
     subset can be retrieved and processed as well.
     SEE: TrainData::setTrainTestSplit</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>ratio</code> - automatically generated</dd>
<dd><code>shuffle</code> - automatically generated</dd>
</dl>
</li>
</ul>
<a name="shuffleTrainTest--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>shuffleTrainTest</h4>
<pre>public&nbsp;void&nbsp;shuffleTrainTest()</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/ml/SVMSGD.html" title="class in org.opencv.ml"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/ml/TrainData.html" target="_top">Frames</a></li>
<li><a href="TrainData.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2024-06-02 16:58:23 / OpenCV 4.10.0</small></p>
</body>
</html>
