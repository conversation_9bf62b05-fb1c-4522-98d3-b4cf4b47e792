{"installationFolder": "E:\\work\\Andriod\\yolo_demo\\opencv410\\build\\intermediates\\prefab_package\\debug\\prefab", "gradlePath": ":opencv410", "packageInfo": {"packageName": "opencv410", "packageSchemaVersion": 2, "packageDependencies": [], "modules": [{"moduleName": "opencv_jni_shared", "moduleHeaders": "E:\\work\\Andriod\\yolo_demo\\opencv410\\native\\jni\\include", "moduleExportLibraries": [], "abis": [{"abiName": "arm64-v8a", "abiApi": 24, "abiNdkMajor": 26, "abiStl": "c++_shared", "abiLibrary": "E:\\work\\Andriod\\yolo_demo\\opencv410\\.cxx\\Debug\\3n2w4716\\arm64-v8a\\libopencv_jni_shared.a", "abiAndroidGradleBuildJsonFile": "E:\\work\\Andriod\\yolo_demo\\opencv410\\.cxx\\Debug\\3n2w4716\\arm64-v8a\\android_gradle_build.json"}]}]}}