{"buildFiles": ["E:\\work\\Andriod\\yolo_demo\\opencv410\\libcxx_helper\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\work\\Andriod\\yolo_demo\\opencv410\\.cxx\\RelWithDebInfo\\r61314v5\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\work\\Andriod\\yolo_demo\\opencv410\\.cxx\\RelWithDebInfo\\r61314v5\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"opencv_jni_shared::@6890427a1f51a3e7e1df": {"toolchain": "toolchain", "abi": "x86", "artifactName": "opencv_jni_shared", "output": "E:\\work\\Andriod\\yolo_demo\\opencv410\\.cxx\\RelWithDebInfo\\r61314v5\\x86\\libopencv_jni_shared.a"}}, "toolchains": {"toolchain": {"cCompilerExecutable": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": ["cpp"]}