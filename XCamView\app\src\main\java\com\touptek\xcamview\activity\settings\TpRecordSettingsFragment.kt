package com.touptek.xcamview.activity.settings
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.RadioGroup
import androidx.fragment.app.Fragment
import com.touptek.xcamview.R

class TpRecordSettingsFragment : Fragment() {
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.settings_record, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // 分辨率设置
        val resolutionGroup = view.findViewById<RadioGroup>(R.id.resolution_group)

        // 视频质量设置
        val qualityGroup = view.findViewById<RadioGroup>(R.id.quality_group)


        // 录制模式设置
        val modeGroup = view.findViewById<RadioGroup>(R.id.mode_group)

        // 功能按钮
        val btnApply = view.findViewById<Button>(R.id.btnApplySettings)
        val btnReset = view.findViewById<Button>(R.id.btnResetDefault)

        // 应用设置按钮点击事件
        btnApply.setOnClickListener {
            applySettings()
        }

        // 恢复默认按钮点击事件
        btnReset.setOnClickListener {
            resetToDefault()
        }

    }

    private fun applySettings() {
        // 获取当前选中的分辨率
        val resolutionId = view?.findViewById<RadioGroup>(R.id.resolution_group)?.checkedRadioButtonId

        // 获取当前选中的视频质量
        val qualityId = view?.findViewById<RadioGroup>(R.id.quality_group)?.checkedRadioButtonId

        // 获取当前录制模式
        val modeId = view?.findViewById<RadioGroup>(R.id.mode_group)?.checkedRadioButtonId

        // 应用设置逻辑...
    }

    private fun resetToDefault() {
        // 重置为默认设置
        view?.findViewById<RadioGroup>(R.id.resolution_group)?.check(R.id.radio_1080p)
        view?.findViewById<RadioGroup>(R.id.quality_group)?.check(R.id.radio_medium)
        view?.findViewById<RadioGroup>(R.id.mode_group)?.check(R.id.radio_normal)
    }

    private fun testRecording() {
        // 测试录制逻辑
    }


}
