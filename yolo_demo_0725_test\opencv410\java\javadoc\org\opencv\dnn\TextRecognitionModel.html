<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_392) on Sun Jun 02 16:58:25 UTC 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>TextRecognitionModel (OpenCV 4.10.0 Java documentation)</title>
<meta name="date" content="2024-06-02">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="TextRecognitionModel (OpenCV 4.10.0 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/dnn/TextDetectionModel_EAST.html" title="class in org.opencv.dnn"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/dnn/TextRecognitionModel.html" target="_top">Frames</a></li>
<li><a href="TextRecognitionModel.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.dnn</div>
<h2 title="Class TextRecognitionModel" class="title">Class TextRecognitionModel</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/dnn/Model.html" title="class in org.opencv.dnn">org.opencv.dnn.Model</a></li>
<li>
<ul class="inheritance">
<li>org.opencv.dnn.TextRecognitionModel</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">TextRecognitionModel</span>
extends <a href="../../../org/opencv/dnn/Model.html" title="class in org.opencv.dnn">Model</a></pre>
<div class="block">This class represents high-level API for text recognition networks.

 TextRecognitionModel allows to set params for preprocessing input image.
 TextRecognitionModel creates net from file with trained weights and config,
 sets preprocessing input, runs forward pass and return recognition result.
 For TextRecognitionModel, CRNN-CTC is supported.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/TextRecognitionModel.html#TextRecognitionModel-org.opencv.dnn.Net-">TextRecognitionModel</a></span>(<a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a>&nbsp;network)</code>
<div class="block">Create Text Recognition model from deep learning network
 Call setDecodeType() and setVocabulary() after constructor to initialize the decoding method</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/TextRecognitionModel.html#TextRecognitionModel-java.lang.String-">TextRecognitionModel</a></span>(java.lang.String&nbsp;model)</code>
<div class="block">Create text recognition model from network represented in one of the supported formats
 Call setDecodeType() and setVocabulary() after constructor to initialize the decoding method</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/TextRecognitionModel.html#TextRecognitionModel-java.lang.String-java.lang.String-">TextRecognitionModel</a></span>(java.lang.String&nbsp;model,
                    java.lang.String&nbsp;config)</code>
<div class="block">Create text recognition model from network represented in one of the supported formats
 Call setDecodeType() and setVocabulary() after constructor to initialize the decoding method</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/dnn/TextRecognitionModel.html" title="class in org.opencv.dnn">TextRecognitionModel</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/TextRecognitionModel.html#Z:Z__fromPtr__-long-">__fromPtr__</a></span>(long&nbsp;addr)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/TextRecognitionModel.html#getDecodeType--">getDecodeType</a></span>()</code>
<div class="block">Get the decoding method</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>java.util.List&lt;java.lang.String&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/TextRecognitionModel.html#getVocabulary--">getVocabulary</a></span>()</code>
<div class="block">Get the vocabulary for recognition.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>java.lang.String</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/TextRecognitionModel.html#recognize-org.opencv.core.Mat-">recognize</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;frame)</code>
<div class="block">Given the <code>input</code> frame, create input blob, run net and return recognition result</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/TextRecognitionModel.html#recognize-org.opencv.core.Mat-java.util.List-java.util.List-">recognize</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;frame,
         java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;roiRects,
         java.util.List&lt;java.lang.String&gt;&nbsp;results)</code>
<div class="block">Given the <code>input</code> frame, create input blob, run net and return recognition result</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/dnn/TextRecognitionModel.html" title="class in org.opencv.dnn">TextRecognitionModel</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/TextRecognitionModel.html#setDecodeOptsCTCPrefixBeamSearch-int-">setDecodeOptsCTCPrefixBeamSearch</a></span>(int&nbsp;beamSize)</code>
<div class="block">Set the decoding method options for <code>"CTC-prefix-beam-search"</code> decode usage</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/dnn/TextRecognitionModel.html" title="class in org.opencv.dnn">TextRecognitionModel</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/TextRecognitionModel.html#setDecodeOptsCTCPrefixBeamSearch-int-int-">setDecodeOptsCTCPrefixBeamSearch</a></span>(int&nbsp;beamSize,
                                int&nbsp;vocPruneSize)</code>
<div class="block">Set the decoding method options for <code>"CTC-prefix-beam-search"</code> decode usage</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../org/opencv/dnn/TextRecognitionModel.html" title="class in org.opencv.dnn">TextRecognitionModel</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/TextRecognitionModel.html#setDecodeType-java.lang.String-">setDecodeType</a></span>(java.lang.String&nbsp;decodeType)</code>
<div class="block">Set the decoding method of translating the network output into string</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../org/opencv/dnn/TextRecognitionModel.html" title="class in org.opencv.dnn">TextRecognitionModel</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/dnn/TextRecognitionModel.html#setVocabulary-java.util.List-">setVocabulary</a></span>(java.util.List&lt;java.lang.String&gt;&nbsp;vocabulary)</code>
<div class="block">Set the vocabulary for recognition.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.dnn.Model">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.dnn.<a href="../../../org/opencv/dnn/Model.html" title="class in org.opencv.dnn">Model</a></h3>
<code><a href="../../../org/opencv/dnn/Model.html#enableWinograd-boolean-">enableWinograd</a>, <a href="../../../org/opencv/dnn/Model.html#getNativeObjAddr--">getNativeObjAddr</a>, <a href="../../../org/opencv/dnn/Model.html#predict-org.opencv.core.Mat-java.util.List-">predict</a>, <a href="../../../org/opencv/dnn/Model.html#setInputCrop-boolean-">setInputCrop</a>, <a href="../../../org/opencv/dnn/Model.html#setInputMean-org.opencv.core.Scalar-">setInputMean</a>, <a href="../../../org/opencv/dnn/Model.html#setInputParams--">setInputParams</a>, <a href="../../../org/opencv/dnn/Model.html#setInputParams-double-">setInputParams</a>, <a href="../../../org/opencv/dnn/Model.html#setInputParams-double-org.opencv.core.Size-">setInputParams</a>, <a href="../../../org/opencv/dnn/Model.html#setInputParams-double-org.opencv.core.Size-org.opencv.core.Scalar-">setInputParams</a>, <a href="../../../org/opencv/dnn/Model.html#setInputParams-double-org.opencv.core.Size-org.opencv.core.Scalar-boolean-">setInputParams</a>, <a href="../../../org/opencv/dnn/Model.html#setInputParams-double-org.opencv.core.Size-org.opencv.core.Scalar-boolean-boolean-">setInputParams</a>, <a href="../../../org/opencv/dnn/Model.html#setInputScale-org.opencv.core.Scalar-">setInputScale</a>, <a href="../../../org/opencv/dnn/Model.html#setInputSize-int-int-">setInputSize</a>, <a href="../../../org/opencv/dnn/Model.html#setInputSize-org.opencv.core.Size-">setInputSize</a>, <a href="../../../org/opencv/dnn/Model.html#setInputSwapRB-boolean-">setInputSwapRB</a>, <a href="../../../org/opencv/dnn/Model.html#setOutputNames-java.util.List-">setOutputNames</a>, <a href="../../../org/opencv/dnn/Model.html#setPreferableBackend-int-">setPreferableBackend</a>, <a href="../../../org/opencv/dnn/Model.html#setPreferableTarget-int-">setPreferableTarget</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="TextRecognitionModel-org.opencv.dnn.Net-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TextRecognitionModel</h4>
<pre>public&nbsp;TextRecognitionModel(<a href="../../../org/opencv/dnn/Net.html" title="class in org.opencv.dnn">Net</a>&nbsp;network)</pre>
<div class="block">Create Text Recognition model from deep learning network
 Call setDecodeType() and setVocabulary() after constructor to initialize the decoding method</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>network</code> - Net object</dd>
</dl>
</li>
</ul>
<a name="TextRecognitionModel-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>TextRecognitionModel</h4>
<pre>public&nbsp;TextRecognitionModel(java.lang.String&nbsp;model)</pre>
<div class="block">Create text recognition model from network represented in one of the supported formats
 Call setDecodeType() and setVocabulary() after constructor to initialize the decoding method</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>model</code> - Binary file contains trained weights</dd>
</dl>
</li>
</ul>
<a name="TextRecognitionModel-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>TextRecognitionModel</h4>
<pre>public&nbsp;TextRecognitionModel(java.lang.String&nbsp;model,
                            java.lang.String&nbsp;config)</pre>
<div class="block">Create text recognition model from network represented in one of the supported formats
 Call setDecodeType() and setVocabulary() after constructor to initialize the decoding method</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>model</code> - Binary file contains trained weights</dd>
<dd><code>config</code> - Text file contains network configuration</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="Z:Z__fromPtr__-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>__fromPtr__</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/dnn/TextRecognitionModel.html" title="class in org.opencv.dnn">TextRecognitionModel</a>&nbsp;__fromPtr__(long&nbsp;addr)</pre>
</li>
</ul>
<a name="getDecodeType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDecodeType</h4>
<pre>public&nbsp;java.lang.String&nbsp;getDecodeType()</pre>
<div class="block">Get the decoding method</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the decoding method</dd>
</dl>
</li>
</ul>
<a name="getVocabulary--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getVocabulary</h4>
<pre>public&nbsp;java.util.List&lt;java.lang.String&gt;&nbsp;getVocabulary()</pre>
<div class="block">Get the vocabulary for recognition.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>vocabulary the associated vocabulary</dd>
</dl>
</li>
</ul>
<a name="recognize-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>recognize</h4>
<pre>public&nbsp;java.lang.String&nbsp;recognize(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;frame)</pre>
<div class="block">Given the <code>input</code> frame, create input blob, run net and return recognition result</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>frame</code> - The input image</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The text recognition result</dd>
</dl>
</li>
</ul>
<a name="recognize-org.opencv.core.Mat-java.util.List-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>recognize</h4>
<pre>public&nbsp;void&nbsp;recognize(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;frame,
                      java.util.List&lt;<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&gt;&nbsp;roiRects,
                      java.util.List&lt;java.lang.String&gt;&nbsp;results)</pre>
<div class="block">Given the <code>input</code> frame, create input blob, run net and return recognition result</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>frame</code> - The input image</dd>
<dd><code>roiRects</code> - List of text detection regions of interest (cv::Rect, CV_32SC4). ROIs is be cropped as the network inputs</dd>
<dd><code>results</code> - A set of text recognition results.</dd>
</dl>
</li>
</ul>
<a name="setDecodeOptsCTCPrefixBeamSearch-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDecodeOptsCTCPrefixBeamSearch</h4>
<pre>public&nbsp;<a href="../../../org/opencv/dnn/TextRecognitionModel.html" title="class in org.opencv.dnn">TextRecognitionModel</a>&nbsp;setDecodeOptsCTCPrefixBeamSearch(int&nbsp;beamSize)</pre>
<div class="block">Set the decoding method options for <code>"CTC-prefix-beam-search"</code> decode usage</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>beamSize</code> - Beam size for search
 only take top <code>vocPruneSize</code> tokens in each search step, <code>vocPruneSize</code> &lt;= 0 stands for disable this prune.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="setDecodeOptsCTCPrefixBeamSearch-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDecodeOptsCTCPrefixBeamSearch</h4>
<pre>public&nbsp;<a href="../../../org/opencv/dnn/TextRecognitionModel.html" title="class in org.opencv.dnn">TextRecognitionModel</a>&nbsp;setDecodeOptsCTCPrefixBeamSearch(int&nbsp;beamSize,
                                                             int&nbsp;vocPruneSize)</pre>
<div class="block">Set the decoding method options for <code>"CTC-prefix-beam-search"</code> decode usage</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>beamSize</code> - Beam size for search</dd>
<dd><code>vocPruneSize</code> - Parameter to optimize big vocabulary search,
 only take top <code>vocPruneSize</code> tokens in each search step, <code>vocPruneSize</code> &lt;= 0 stands for disable this prune.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="setDecodeType-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDecodeType</h4>
<pre>public&nbsp;<a href="../../../org/opencv/dnn/TextRecognitionModel.html" title="class in org.opencv.dnn">TextRecognitionModel</a>&nbsp;setDecodeType(java.lang.String&nbsp;decodeType)</pre>
<div class="block">Set the decoding method of translating the network output into string</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>decodeType</code> - The decoding method of translating the network output into string, currently supported type:
 - <code>"CTC-greedy"</code> greedy decoding for the output of CTC-based methods
 - <code>"CTC-prefix-beam-search"</code> Prefix beam search decoding for the output of CTC-based methods</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="setVocabulary-java.util.List-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setVocabulary</h4>
<pre>public&nbsp;<a href="../../../org/opencv/dnn/TextRecognitionModel.html" title="class in org.opencv.dnn">TextRecognitionModel</a>&nbsp;setVocabulary(java.util.List&lt;java.lang.String&gt;&nbsp;vocabulary)</pre>
<div class="block">Set the vocabulary for recognition.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>vocabulary</code> - the associated vocabulary of the network.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/dnn/TextDetectionModel_EAST.html" title="class in org.opencv.dnn"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/dnn/TextRecognitionModel.html" target="_top">Frames</a></li>
<li><a href="TextRecognitionModel.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2024-06-02 16:58:23 / OpenCV 4.10.0</small></p>
</body>
</html>
