<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\work\Andriod\yolo_demo\opencv410\native\libs"><file name="arm64-v8a/libopencv_java4.so" path="E:\work\Andriod\yolo_demo\opencv410\native\libs\arm64-v8a\libopencv_java4.so"/><file name="armeabi-v7a/libopencv_java4.so" path="E:\work\Andriod\yolo_demo\opencv410\native\libs\armeabi-v7a\libopencv_java4.so"/><file name="x86/libopencv_java4.so" path="E:\work\Andriod\yolo_demo\opencv410\native\libs\x86\libopencv_java4.so"/><file name="x86_64/libopencv_java4.so" path="E:\work\Andriod\yolo_demo\opencv410\native\libs\x86_64\libopencv_java4.so"/></source></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\work\Andriod\yolo_demo\opencv410\src\debug\jniLibs"/></dataSet></merger>