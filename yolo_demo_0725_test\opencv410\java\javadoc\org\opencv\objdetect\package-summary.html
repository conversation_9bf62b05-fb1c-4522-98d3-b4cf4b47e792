<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_392) on Sun Jun 02 16:58:28 UTC 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>org.opencv.objdetect (OpenCV 4.10.0 Java documentation)</title>
<meta name="date" content="2024-06-02">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="org.opencv.objdetect (OpenCV 4.10.0 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/ml/package-summary.html">Prev&nbsp;Package</a></li>
<li><a href="../../../org/opencv/osgi/package-summary.html">Next&nbsp;Package</a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/objdetect/package-summary.html" target="_top">Frames</a></li>
<li><a href="package-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Package" class="title">Package&nbsp;org.opencv.objdetect</h1>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Class Summary table, listing classes, and an explanation">
<caption><span>Class Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Class</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../org/opencv/objdetect/ArucoDetector.html" title="class in org.opencv.objdetect">ArucoDetector</a></td>
<td class="colLast">
<div class="block">The main functionality of ArucoDetector class is detection of markers in an image with detectMarkers() method.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../org/opencv/objdetect/BarcodeDetector.html" title="class in org.opencv.objdetect">BarcodeDetector</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../org/opencv/objdetect/BaseCascadeClassifier.html" title="class in org.opencv.objdetect">BaseCascadeClassifier</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../org/opencv/objdetect/Board.html" title="class in org.opencv.objdetect">Board</a></td>
<td class="colLast">
<div class="block">Board of ArUco markers

 A board is a set of markers in the 3D space with a common coordinate system.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../org/opencv/objdetect/CascadeClassifier.html" title="class in org.opencv.objdetect">CascadeClassifier</a></td>
<td class="colLast">
<div class="block">Cascade classifier class for object detection.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../org/opencv/objdetect/CharucoBoard.html" title="class in org.opencv.objdetect">CharucoBoard</a></td>
<td class="colLast">
<div class="block">ChArUco board is a planar chessboard where the markers are placed inside the white squares of a chessboard.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../org/opencv/objdetect/CharucoDetector.html" title="class in org.opencv.objdetect">CharucoDetector</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../org/opencv/objdetect/CharucoParameters.html" title="class in org.opencv.objdetect">CharucoParameters</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../org/opencv/objdetect/DetectorParameters.html" title="class in org.opencv.objdetect">DetectorParameters</a></td>
<td class="colLast">
<div class="block">struct DetectorParameters is used by ArucoDetector</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../org/opencv/objdetect/Dictionary.html" title="class in org.opencv.objdetect">Dictionary</a></td>
<td class="colLast">
<div class="block">Dictionary is a set of unique ArUco markers of the same size

 <code>bytesList</code> storing as 2-dimensions Mat with 4-th channels (CV_8UC4 type was used) and contains the marker codewords where:
 - bytesList.rows is the dictionary size
 - each marker is encoded using <code>nbytes = ceil(markerSize*markerSize/8.)</code> bytes
 - each row contains all 4 rotations of the marker, so its length is <code>4*nbytes</code>
 - the byte order in the bytesList[i] row:
 <code>//bytes without rotation/bytes with rotation 1/bytes with rotation 2/bytes with rotation 3//</code>
 So <code>bytesList.ptr(i)[k*nbytes + j]</code> is the j-th byte of i-th marker, in its k-th rotation.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../org/opencv/objdetect/FaceDetectorYN.html" title="class in org.opencv.objdetect">FaceDetectorYN</a></td>
<td class="colLast">
<div class="block">DNN-based face detector

 model download link: https://github.com/opencv/opencv_zoo/tree/master/models/face_detection_yunet</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../org/opencv/objdetect/FaceRecognizerSF.html" title="class in org.opencv.objdetect">FaceRecognizerSF</a></td>
<td class="colLast">
<div class="block">DNN-based face recognizer

 model download link: https://github.com/opencv/opencv_zoo/tree/master/models/face_recognition_sface</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../org/opencv/objdetect/GraphicalCodeDetector.html" title="class in org.opencv.objdetect">GraphicalCodeDetector</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../org/opencv/objdetect/GridBoard.html" title="class in org.opencv.objdetect">GridBoard</a></td>
<td class="colLast">
<div class="block">Planar board with grid arrangement of markers

 More common type of board.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../org/opencv/objdetect/HOGDescriptor.html" title="class in org.opencv.objdetect">HOGDescriptor</a></td>
<td class="colLast">
<div class="block">Implementation of HOG (Histogram of Oriented Gradients) descriptor and object detector.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../org/opencv/objdetect/Objdetect.html" title="class in org.opencv.objdetect">Objdetect</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../org/opencv/objdetect/QRCodeDetector.html" title="class in org.opencv.objdetect">QRCodeDetector</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../org/opencv/objdetect/QRCodeDetectorAruco.html" title="class in org.opencv.objdetect">QRCodeDetectorAruco</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../org/opencv/objdetect/QRCodeDetectorAruco_Params.html" title="class in org.opencv.objdetect">QRCodeDetectorAruco_Params</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../org/opencv/objdetect/QRCodeEncoder.html" title="class in org.opencv.objdetect">QRCodeEncoder</a></td>
<td class="colLast">
<div class="block">Groups the object candidate rectangles.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../org/opencv/objdetect/QRCodeEncoder_Params.html" title="class in org.opencv.objdetect">QRCodeEncoder_Params</a></td>
<td class="colLast">
<div class="block">QR code encoder parameters.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../org/opencv/objdetect/RefineParameters.html" title="class in org.opencv.objdetect">RefineParameters</a></td>
<td class="colLast">
<div class="block">struct RefineParameters is used by ArucoDetector</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/ml/package-summary.html">Prev&nbsp;Package</a></li>
<li><a href="../../../org/opencv/osgi/package-summary.html">Next&nbsp;Package</a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/objdetect/package-summary.html" target="_top">Frames</a></li>
<li><a href="package-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2024-06-02 16:58:23 / OpenCV 4.10.0</small></p>
</body>
</html>
