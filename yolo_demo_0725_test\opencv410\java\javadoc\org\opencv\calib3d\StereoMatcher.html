<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_392) on Sun Jun 02 16:58:27 UTC 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>StereoMatcher (OpenCV 4.10.0 Java documentation)</title>
<meta name="date" content="2024-06-02">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="StereoMatcher (OpenCV 4.10.0 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/calib3d/StereoBM.html" title="class in org.opencv.calib3d"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/calib3d/StereoSGBM.html" title="class in org.opencv.calib3d"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/calib3d/StereoMatcher.html" target="_top">Frames</a></li>
<li><a href="StereoMatcher.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.calib3d</div>
<h2 title="Class StereoMatcher" class="title">Class StereoMatcher</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">org.opencv.core.Algorithm</a></li>
<li>
<ul class="inheritance">
<li>org.opencv.calib3d.StereoMatcher</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>Direct Known Subclasses:</dt>
<dd><a href="../../../org/opencv/calib3d/StereoBM.html" title="class in org.opencv.calib3d">StereoBM</a>, <a href="../../../org/opencv/calib3d/StereoSGBM.html" title="class in org.opencv.calib3d">StereoSGBM</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">StereoMatcher</span>
extends <a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></pre>
<div class="block">The base class for stereo correspondence algorithms.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoMatcher.html#DISP_SCALE">DISP_SCALE</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoMatcher.html#DISP_SHIFT">DISP_SHIFT</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/calib3d/StereoMatcher.html" title="class in org.opencv.calib3d">StereoMatcher</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoMatcher.html#Z:Z__fromPtr__-long-">__fromPtr__</a></span>(long&nbsp;addr)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoMatcher.html#compute-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">compute</a></span>(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;left,
       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;right,
       <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;disparity)</code>
<div class="block">Computes disparity map for the specified stereo pair</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoMatcher.html#getBlockSize--">getBlockSize</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoMatcher.html#getDisp12MaxDiff--">getDisp12MaxDiff</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoMatcher.html#getMinDisparity--">getMinDisparity</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoMatcher.html#getNumDisparities--">getNumDisparities</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoMatcher.html#getSpeckleRange--">getSpeckleRange</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoMatcher.html#getSpeckleWindowSize--">getSpeckleWindowSize</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoMatcher.html#setBlockSize-int-">setBlockSize</a></span>(int&nbsp;blockSize)</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoMatcher.html#setDisp12MaxDiff-int-">setDisp12MaxDiff</a></span>(int&nbsp;disp12MaxDiff)</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoMatcher.html#setMinDisparity-int-">setMinDisparity</a></span>(int&nbsp;minDisparity)</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoMatcher.html#setNumDisparities-int-">setNumDisparities</a></span>(int&nbsp;numDisparities)</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoMatcher.html#setSpeckleRange-int-">setSpeckleRange</a></span>(int&nbsp;speckleRange)</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoMatcher.html#setSpeckleWindowSize-int-">setSpeckleWindowSize</a></span>(int&nbsp;speckleWindowSize)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.core.Algorithm">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.core.<a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></h3>
<code><a href="../../../org/opencv/core/Algorithm.html#clear--">clear</a>, <a href="../../../org/opencv/core/Algorithm.html#empty--">empty</a>, <a href="../../../org/opencv/core/Algorithm.html#getDefaultName--">getDefaultName</a>, <a href="../../../org/opencv/core/Algorithm.html#getNativeObjAddr--">getNativeObjAddr</a>, <a href="../../../org/opencv/core/Algorithm.html#save-java.lang.String-">save</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="DISP_SCALE">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DISP_SCALE</h4>
<pre>public static final&nbsp;int DISP_SCALE</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.calib3d.StereoMatcher.DISP_SCALE">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="DISP_SHIFT">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>DISP_SHIFT</h4>
<pre>public static final&nbsp;int DISP_SHIFT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.calib3d.StereoMatcher.DISP_SHIFT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="Z:Z__fromPtr__-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>__fromPtr__</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/calib3d/StereoMatcher.html" title="class in org.opencv.calib3d">StereoMatcher</a>&nbsp;__fromPtr__(long&nbsp;addr)</pre>
</li>
</ul>
<a name="compute-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>compute</h4>
<pre>public&nbsp;void&nbsp;compute(<a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;left,
                    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;right,
                    <a href="../../../org/opencv/core/Mat.html" title="class in org.opencv.core">Mat</a>&nbsp;disparity)</pre>
<div class="block">Computes disparity map for the specified stereo pair</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>left</code> - Left 8-bit single-channel image.</dd>
<dd><code>right</code> - Right image of the same size and the same type as the left one.</dd>
<dd><code>disparity</code> - Output disparity map. It has the same size as the input images. Some algorithms,
     like StereoBM or StereoSGBM compute 16-bit fixed-point disparity map (where each disparity value
     has 4 fractional bits), whereas other algorithms output 32-bit floating-point disparity map.</dd>
</dl>
</li>
</ul>
<a name="getBlockSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBlockSize</h4>
<pre>public&nbsp;int&nbsp;getBlockSize()</pre>
</li>
</ul>
<a name="getDisp12MaxDiff--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDisp12MaxDiff</h4>
<pre>public&nbsp;int&nbsp;getDisp12MaxDiff()</pre>
</li>
</ul>
<a name="getMinDisparity--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMinDisparity</h4>
<pre>public&nbsp;int&nbsp;getMinDisparity()</pre>
</li>
</ul>
<a name="getNumDisparities--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNumDisparities</h4>
<pre>public&nbsp;int&nbsp;getNumDisparities()</pre>
</li>
</ul>
<a name="getSpeckleRange--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSpeckleRange</h4>
<pre>public&nbsp;int&nbsp;getSpeckleRange()</pre>
</li>
</ul>
<a name="getSpeckleWindowSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSpeckleWindowSize</h4>
<pre>public&nbsp;int&nbsp;getSpeckleWindowSize()</pre>
</li>
</ul>
<a name="setBlockSize-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBlockSize</h4>
<pre>public&nbsp;void&nbsp;setBlockSize(int&nbsp;blockSize)</pre>
</li>
</ul>
<a name="setDisp12MaxDiff-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDisp12MaxDiff</h4>
<pre>public&nbsp;void&nbsp;setDisp12MaxDiff(int&nbsp;disp12MaxDiff)</pre>
</li>
</ul>
<a name="setMinDisparity-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMinDisparity</h4>
<pre>public&nbsp;void&nbsp;setMinDisparity(int&nbsp;minDisparity)</pre>
</li>
</ul>
<a name="setNumDisparities-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setNumDisparities</h4>
<pre>public&nbsp;void&nbsp;setNumDisparities(int&nbsp;numDisparities)</pre>
</li>
</ul>
<a name="setSpeckleRange-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSpeckleRange</h4>
<pre>public&nbsp;void&nbsp;setSpeckleRange(int&nbsp;speckleRange)</pre>
</li>
</ul>
<a name="setSpeckleWindowSize-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setSpeckleWindowSize</h4>
<pre>public&nbsp;void&nbsp;setSpeckleWindowSize(int&nbsp;speckleWindowSize)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/calib3d/StereoBM.html" title="class in org.opencv.calib3d"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/calib3d/StereoSGBM.html" title="class in org.opencv.calib3d"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/calib3d/StereoMatcher.html" target="_top">Frames</a></li>
<li><a href="StereoMatcher.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2024-06-02 16:58:23 / OpenCV 4.10.0</small></p>
</body>
</html>
