<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_392) on Sun Jun 02 16:58:25 UTC 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>CameraGLRendererBase (OpenCV 4.10.0 Java documentation)</title>
<meta name="date" content="2024-06-02">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="CameraGLRendererBase (OpenCV 4.10.0 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/android/CameraBridgeViewBase.RotatedCameraFrame.html" title="class in org.opencv.android"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/android/CameraGLSurfaceView.html" title="class in org.opencv.android"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/android/CameraGLRendererBase.html" target="_top">Frames</a></li>
<li><a href="CameraGLRendererBase.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.android</div>
<h2 title="Class CameraGLRendererBase" class="title">Class CameraGLRendererBase</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.opencv.android.CameraGLRendererBase</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd>android.graphics.SurfaceTexture.OnFrameAvailableListener, android.opengl.GLSurfaceView.Renderer</dd>
</dl>
<dl>
<dt>Direct Known Subclasses:</dt>
<dd><a href="../../../org/opencv/android/Camera2Renderer.html" title="class in org.opencv.android">Camera2Renderer</a>, <a href="../../../org/opencv/android/CameraRenderer.html" title="class in org.opencv.android">CameraRenderer</a></dd>
</dl>
<hr>
<br>
<pre>public abstract class <span class="typeNameLabel">CameraGLRendererBase</span>
extends java.lang.Object
implements android.opengl.GLSurfaceView.Renderer, android.graphics.SurfaceTexture.OnFrameAvailableListener</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/android/CameraGLRendererBase.html#CameraGLRendererBase-org.opencv.android.CameraGLSurfaceView-">CameraGLRendererBase</a></span>(<a href="../../../org/opencv/android/CameraGLSurfaceView.html" title="class in org.opencv.android">CameraGLSurfaceView</a>&nbsp;view)</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/android/CameraGLRendererBase.html#disableView--">disableView</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/android/CameraGLRendererBase.html#enableView--">enableView</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/android/CameraGLRendererBase.html#onDrawFrame-javax.microedition.khronos.opengles.GL10-">onDrawFrame</a></span>(javax.microedition.khronos.opengles.GL10&nbsp;gl)</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/android/CameraGLRendererBase.html#onFrameAvailable-android.graphics.SurfaceTexture-">onFrameAvailable</a></span>(android.graphics.SurfaceTexture&nbsp;surfaceTexture)</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/android/CameraGLRendererBase.html#onPause--">onPause</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/android/CameraGLRendererBase.html#onResume--">onResume</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/android/CameraGLRendererBase.html#onSurfaceChanged-javax.microedition.khronos.opengles.GL10-int-int-">onSurfaceChanged</a></span>(javax.microedition.khronos.opengles.GL10&nbsp;gl,
                int&nbsp;surfaceWidth,
                int&nbsp;surfaceHeight)</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/android/CameraGLRendererBase.html#onSurfaceCreated-javax.microedition.khronos.opengles.GL10-javax.microedition.khronos.egl.EGLConfig-">onSurfaceCreated</a></span>(javax.microedition.khronos.opengles.GL10&nbsp;gl,
                javax.microedition.khronos.egl.EGLConfig&nbsp;config)</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/android/CameraGLRendererBase.html#setCameraIndex-int-">setCameraIndex</a></span>(int&nbsp;cameraIndex)</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/android/CameraGLRendererBase.html#setMaxCameraPreviewSize-int-int-">setMaxCameraPreviewSize</a></span>(int&nbsp;maxWidth,
                       int&nbsp;maxHeight)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="CameraGLRendererBase-org.opencv.android.CameraGLSurfaceView-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>CameraGLRendererBase</h4>
<pre>public&nbsp;CameraGLRendererBase(<a href="../../../org/opencv/android/CameraGLSurfaceView.html" title="class in org.opencv.android">CameraGLSurfaceView</a>&nbsp;view)</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="disableView--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>disableView</h4>
<pre>public&nbsp;void&nbsp;disableView()</pre>
</li>
</ul>
<a name="enableView--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>enableView</h4>
<pre>public&nbsp;void&nbsp;enableView()</pre>
</li>
</ul>
<a name="onDrawFrame-javax.microedition.khronos.opengles.GL10-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onDrawFrame</h4>
<pre>public&nbsp;void&nbsp;onDrawFrame(javax.microedition.khronos.opengles.GL10&nbsp;gl)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>onDrawFrame</code>&nbsp;in interface&nbsp;<code>android.opengl.GLSurfaceView.Renderer</code></dd>
</dl>
</li>
</ul>
<a name="onFrameAvailable-android.graphics.SurfaceTexture-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onFrameAvailable</h4>
<pre>public&nbsp;void&nbsp;onFrameAvailable(android.graphics.SurfaceTexture&nbsp;surfaceTexture)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>onFrameAvailable</code>&nbsp;in interface&nbsp;<code>android.graphics.SurfaceTexture.OnFrameAvailableListener</code></dd>
</dl>
</li>
</ul>
<a name="onPause--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onPause</h4>
<pre>public&nbsp;void&nbsp;onPause()</pre>
</li>
</ul>
<a name="onResume--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onResume</h4>
<pre>public&nbsp;void&nbsp;onResume()</pre>
</li>
</ul>
<a name="onSurfaceChanged-javax.microedition.khronos.opengles.GL10-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onSurfaceChanged</h4>
<pre>public&nbsp;void&nbsp;onSurfaceChanged(javax.microedition.khronos.opengles.GL10&nbsp;gl,
                             int&nbsp;surfaceWidth,
                             int&nbsp;surfaceHeight)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>onSurfaceChanged</code>&nbsp;in interface&nbsp;<code>android.opengl.GLSurfaceView.Renderer</code></dd>
</dl>
</li>
</ul>
<a name="onSurfaceCreated-javax.microedition.khronos.opengles.GL10-javax.microedition.khronos.egl.EGLConfig-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>onSurfaceCreated</h4>
<pre>public&nbsp;void&nbsp;onSurfaceCreated(javax.microedition.khronos.opengles.GL10&nbsp;gl,
                             javax.microedition.khronos.egl.EGLConfig&nbsp;config)</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code>onSurfaceCreated</code>&nbsp;in interface&nbsp;<code>android.opengl.GLSurfaceView.Renderer</code></dd>
</dl>
</li>
</ul>
<a name="setCameraIndex-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCameraIndex</h4>
<pre>public&nbsp;void&nbsp;setCameraIndex(int&nbsp;cameraIndex)</pre>
</li>
</ul>
<a name="setMaxCameraPreviewSize-int-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setMaxCameraPreviewSize</h4>
<pre>public&nbsp;void&nbsp;setMaxCameraPreviewSize(int&nbsp;maxWidth,
                                    int&nbsp;maxHeight)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/android/CameraBridgeViewBase.RotatedCameraFrame.html" title="class in org.opencv.android"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/android/CameraGLSurfaceView.html" title="class in org.opencv.android"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/android/CameraGLRendererBase.html" target="_top">Frames</a></li>
<li><a href="CameraGLRendererBase.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2024-06-02 16:58:23 / OpenCV 4.10.0</small></p>
</body>
</html>
