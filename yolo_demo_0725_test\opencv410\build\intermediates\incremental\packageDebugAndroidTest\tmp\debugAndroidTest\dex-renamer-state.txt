#Mon Jun 30 16:51:26 CST 2025
base.0=E\:\\work\\Andriod\\yolo_demo\\opencv410\\build\\intermediates\\dex\\debugAndroidTest\\mergeExtDexDebugAndroidTest\\classes.dex
base.1=E\:\\work\\Andriod\\yolo_demo\\opencv410\\build\\intermediates\\dex\\debugAndroidTest\\mergeLibDexDebugAndroidTest\\10\\classes.dex
base.10=E\:\\work\\Andriod\\yolo_demo\\opencv410\\build\\intermediates\\dex\\debugAndroidTest\\mergeProjectDexDebugAndroidTest\\2\\classes.dex
base.2=E\:\\work\\Andriod\\yolo_demo\\opencv410\\build\\intermediates\\dex\\debugAndroidTest\\mergeLibDexDebugAndroidTest\\12\\classes.dex
base.3=E\:\\work\\Andriod\\yolo_demo\\opencv410\\build\\intermediates\\dex\\debugAndroidTest\\mergeLibDexDebugAndroidTest\\13\\classes.dex
base.4=E\:\\work\\Andriod\\yolo_demo\\opencv410\\build\\intermediates\\dex\\debugAndroidTest\\mergeLibDexDebugAndroidTest\\1\\classes.dex
base.5=E\:\\work\\Andriod\\yolo_demo\\opencv410\\build\\intermediates\\dex\\debugAndroidTest\\mergeLibDexDebugAndroidTest\\2\\classes.dex
base.6=E\:\\work\\Andriod\\yolo_demo\\opencv410\\build\\intermediates\\dex\\debugAndroidTest\\mergeLibDexDebugAndroidTest\\6\\classes.dex
base.7=E\:\\work\\Andriod\\yolo_demo\\opencv410\\build\\intermediates\\dex\\debugAndroidTest\\mergeLibDexDebugAndroidTest\\8\\classes.dex
base.8=E\:\\work\\Andriod\\yolo_demo\\opencv410\\build\\intermediates\\dex\\debugAndroidTest\\mergeLibDexDebugAndroidTest\\9\\classes.dex
base.9=E\:\\work\\Andriod\\yolo_demo\\opencv410\\build\\intermediates\\dex\\debugAndroidTest\\mergeProjectDexDebugAndroidTest\\0\\classes.dex
path.0=classes.dex
path.1=10/classes.dex
path.10=2/classes.dex
path.2=12/classes.dex
path.3=13/classes.dex
path.4=1/classes.dex
path.5=2/classes.dex
path.6=6/classes.dex
path.7=8/classes.dex
path.8=9/classes.dex
path.9=0/classes.dex
renamed.0=classes.dex
renamed.1=classes2.dex
renamed.10=classes11.dex
renamed.2=classes3.dex
renamed.3=classes4.dex
renamed.4=classes5.dex
renamed.5=classes6.dex
renamed.6=classes7.dex
renamed.7=classes8.dex
renamed.8=classes9.dex
renamed.9=classes10.dex
