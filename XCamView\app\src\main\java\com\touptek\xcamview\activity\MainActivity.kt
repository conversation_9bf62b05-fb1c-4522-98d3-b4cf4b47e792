package com.touptek.xcamview.activity

import android.content.Intent
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.SurfaceTexture
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.ScaleGestureDetector
import android.view.Surface
import android.view.TextureView
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.TextView
import android.widget.Toast
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.DialogFragment
import com.touptek.xcamview.activity.browse.TpVideoBrowse
import com.touptek.xcamview.activity.ispdialogfragment.wbroimanagement.TpRectangleOverlayView
import com.touptek.xcamview.databinding.ActivityMainBinding
import com.touptek.utils.TpFileManager
import com.touptek.video.TpIspParam
import com.touptek.ui.internal.TpViewTransform
import com.touptek.xcamview.util.getStorageDCIMPath
import com.touptek.xcamview.util.getStoragePicturePath
import com.touptek.xcamview.util.getStorageVideoPath
import com.touptek.xcamview.util.setupEdgeToEdgeFullScreen
import com.touptek.xcamview.view.MeasurementOverlayView
import com.touptek.video.TpVideoConfig
import com.touptek.video.TpVideoSystem
import com.touptek.ui.TpRoiView
import com.touptek.utils.TpHdmiMonitor
import com.touptek.xcamview.util.BaseActivity
import java.io.File
import java.io.FileOutputStream
import kotlin.apply
import kotlin.collections.dropLast
import kotlin.collections.joinToString
import kotlin.io.use
import kotlin.jvm.java
import kotlin.jvm.javaClass
import kotlin.let
import kotlin.run
import kotlin.text.contains
import kotlin.text.format
import kotlin.text.split
import kotlin.text.substringAfterLast
import kotlin.text.trimEnd

class StatusBanner(private val view: TextView) {
//    // 添加带默认值的duration参数
//    private var hideRunnable: Runnable? = null
//
//    fun show(text: String, duration: Long = 3000) {
//        view.post {
//            // 取消之前的隐藏任务（如果存在）
//            hideRunnable?.let { view.removeCallbacks(it) }
//
//            // 显示横幅
//            view.apply {
//                this.text = text
//                visibility = View.VISIBLE
//            }
//
//            // 设置新的隐藏任务
//            hideRunnable = Runnable {
//                view.visibility = View.GONE
//            }
//            view.postDelayed(hideRunnable, duration)
//        }
//    }
//
//
//    fun hide() {
//        view.post {
//            // 取消等待中的隐藏任务
//            hideRunnable?.let { view.removeCallbacks(it) }
//            // 立即隐藏横幅
//            view.visibility = View.GONE
//        }
//    }

    private var hideRunnable: Runnable? = null
    private val fadeDuration = 300L // 淡入淡出动画时长

    fun show(text: String, duration: Long = 3000) {
        view.post {
            // 取消之前的隐藏任务
            hideRunnable?.let { view.removeCallbacks(it) }
            hideRunnable = null

            // 更新文本内容
            view.text = text

            // 显示动画
            view.visibility = View.VISIBLE
            view.animate()
                .alpha(1f)
                .setDuration(fadeDuration)
                .withStartAction { view.alpha = 0f }
                .start()

            // 设置新的隐藏任务（带淡出动画）
            hideRunnable = Runnable {
                view.animate()
                    .alpha(0f)
                    .setDuration(fadeDuration)
                    .withEndAction { view.visibility = View.GONE }
                    .start()
            }
            view.postDelayed(hideRunnable, duration)
        }
    }

    fun hide() {
        view.post {
            hideRunnable?.let { view.removeCallbacks(it) }
            hideRunnable = null

            view.animate()
                .alpha(0f)
                .setDuration(fadeDuration)
                .withEndAction { view.visibility = View.GONE }
                .start()
        }
    }
}

class MainActivity : BaseActivity(), View.OnAttachStateChangeListener, MainMenu.OnRectangleVisibilityListener {
    private lateinit var binding: ActivityMainBinding
    private var isActivityResumed = false
    private var isSerialConnected = false
    private var isHDMIConnected = false

    private val TAG = "MainActivity"

    //HDMI
    private var tpHdmiMonitor: TpHdmiMonitor? = null
    private var isMenuEnabled: Boolean = true
    private var isCameraOpened: Boolean = false
    fun isMenuEnabled(): Boolean = isMenuEnabled

    private var startTime: Long = 0
    private val handler = Handler(Looper.getMainLooper())
    private lateinit var updateTimeRunnable: Runnable

    private lateinit var banner: StatusBanner  //提示标签
    private var isRecording = false

    //zoom
    private var scaleGestureDetector: ScaleGestureDetector? = null
    private var gestureDetector: GestureDetector? = null
    //----Test
    // 在类中添加这些成员变量
    private var initialX = 0f
    private var initialY = 0f
    private var touchCount = 0
    private var hasMovedSignificantly = false
    private var hasPerformedZoom = false
    private val MOVE_THRESHOLD = 20f // 移动阈值50像素
    //----

    //roi
    private var rectangleOverlayView: TpRectangleOverlayView? = null
    private var tpRoiView: TpRoiView? = null

    //关联串口
    private var videoSystem: TpVideoSystem? = null

    private fun getMainMenuFragment(): MainMenu? {
        return supportFragmentManager.findFragmentByTag("MainMenu") as? MainMenu
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)
        setupEdgeToEdgeFullScreen() //全屏

        // 按照 VideoTest 最佳实践：在 onCreate 中创建 TpVideoSystem
        val config = TpVideoConfig.createDefault4K()
        videoSystem = TpVideoSystem(this, config)

        Log.d(TAG, "TpVideoSystem 创建完成，配置: ${config.toString()}")

        // 设置 TpVideoSystem 监听器（使用适配器模式）
        setupVideoSystemListener()

        // 设置 TV 容器（必须在使用 TV 模式之前设置）
        setupTpVideoSystemTvMode()

        // 初始化UI和功能组件
        initCameraMode()  // 设置UI模式
        initStorageMonitor()

        // 初始化触摸和手势功能
        initScaleGestureDetector()
        initPanGestureDetector()
        initTouchEvent()

        // 设置ISP开机记忆参数，恢复上一次的参数设置
        initISP()
        initCameraMemoryParameter()

        // 初始化ROI相关控件
        initializeRoiComponents()

        banner = StatusBanner(binding.mainCenterInfoLabel)

        // 最后初始化 HDMI 服务
        initHdmiService()
    }

    /**
     * 设置 TpVideoSystem 监听器（按照 VideoTest 最佳实践）
     */
    private fun setupVideoSystemListener() {
        videoSystem?.setListener(object : TpVideoSystem.TpVideoSystemAdapter() {
            override fun onError(errorMessage: String) {
                Log.e(TAG, "TpVideoSystem 错误: $errorMessage")
                runOnUiThread {
                    // 针对 MediaCodec 错误的特殊处理
                    if (errorMessage.contains("MediaCodec") || errorMessage.contains("Codec") || errorMessage.contains("编码")) {
                        banner.show("编码器错误，正在重新初始化...", 3000)
                        // 延迟重新初始化
                        handler.postDelayed({
                            if (binding.blueTextureView.isAvailable) {
                                Log.d(TAG, "MediaCodec 错误后重新初始化")
                                releaseResources()
                                handler.postDelayed({
                                    setupViewListener()
                                }, 1000)
                            }
                        }, 2000)
                    } else {
                        banner.show("系统错误: $errorMessage", 3000)
                    }
                }
            }

            override fun onCameraStarted() {
                Log.d(TAG, "相机启动完成")
                runOnUiThread {
                    isCameraOpened = true
                    // 相机启动后，设置ROI视图的相机分辨率
                    videoSystem?.let { vs ->
                        val currentSize = vs.videoConfig.size
                        binding.roiView.setCameraResolution(currentSize.width, currentSize.height)
                        Log.d(TAG, "ROI视图相机分辨率已设置: ${currentSize.width}x${currentSize.height}")
                    }
                }
            }

            override fun onRecordingStarted(outputPath: String) {
                Log.d(TAG, "录制已开始: $outputPath")
                runOnUiThread {
                    isRecording = true
                    updateMainMenuRecordButton()
                    banner.show("开始录像", 1000)
                    startTimer()
                }
            }

            override fun onRecordingStopped(outputPath: String) {
                Log.d(TAG, "录制已停止: $outputPath")
                runOnUiThread {
                    isRecording = false
                    updateMainMenuRecordButton()
                    binding.tvTimer.visibility = View.INVISIBLE
                    val fileName = outputPath.trimEnd('/').substringAfterLast("/")
                    banner.show("${fileName}已保存", 1500)
                }
            }

            override fun onImageCaptured(imagePath: String) {
                Log.d(TAG, "图像捕获完成: $imagePath")
                runOnUiThread {
                    banner.show("捕获成功", 1000)
                }
            }
        })
    }

    /**
     * 为 TpVideoSystem 设置 TV 模式支持（按照 VideoTest 最佳实践）
     */
    private fun setupTpVideoSystemTvMode() {
        videoSystem?.let { vs ->
            // 使用根视图作为 TV 预览容器
            val tvContainer = binding.root as? ViewGroup
            if (tvContainer != null) {
                // 设置 TV 容器
                vs.setTvContainer(tvContainer)
                Log.d(TAG, "TpVideoSystem TV容器已设置: ${tvContainer.javaClass.simpleName}")
            } else {
                Log.e(TAG, "根视图不是 ViewGroup，无法设置 TV 模式")
            }
        } ?: run {
            Log.e(TAG, "TpVideoSystem 未创建，无法设置 TV 模式")
        }
    }

    override fun onViewAttachedToWindow(v: View) = Unit //空实现
    override fun onViewDetachedFromWindow(v: View) = Unit



    /**
     * 初始化 HDMI 服务（按照 VideoTest 最佳实践）
     */
    private fun initHdmiService() {
        Log.d(TAG, "初始化 HDMI 服务")

        tpHdmiMonitor = TpHdmiMonitor.getInstance()
        // 先设置监听器再进行初始化，防止丢失状态
        tpHdmiMonitor?.setHdmiListener { isConnected: Boolean ->
            runOnUiThread {
                handleHdmiStatusChange(isConnected)
            }
        }

        tpHdmiMonitor?.init()
        setupViewListener()
        Log.d(TAG, "HDMI 服务已初始化")
    }

    /**
     * 处理 HDMI 状态变化（按照 VideoTest 最佳实践）
     */
    private fun handleHdmiStatusChange(isConnected: Boolean) {
        Log.d(TAG, "HDMI状态变化: ${if (isConnected) "已连接" else "已断开"}")

        // 如果状态没有变化，不处理
        if (isHDMIConnected == isConnected) {
            return
        }

        isHDMIConnected = isConnected

        // 使用延迟处理，确保状态稳定（按照 VideoTest 模式）
        handler.postDelayed({
            if (isConnected) {
                handleHdmiConnected()
            } else {
                handleHdmiDisconnected()
            }
        }, 500) // 延迟500毫秒处理
    }

    /**
     * 处理 HDMI 连接事件
     */
    private fun handleHdmiConnected() {
        Log.d(TAG, "处理HDMI连接")

        if (!<EMAIL>()) {
            return
        }

        runOnUiThread {
            banner.show("HDMI连接", 1000)

            videoSystem?.let { vs ->
                // 检查 TpVideoSystem 状态
                if (!vs.isInitialized()) {
                    Log.w(TAG, "TpVideoSystem 尚未初始化，等待初始化完成")
                    banner.show("正在初始化相机系统...", 2000)
                    // 延迟重试
                    handler.postDelayed({
                        if (isHDMIConnected) {
                            handleHdmiConnected() // 重试
                        }
                    }, 1000)
                    return@runOnUiThread
                }

                // 如果系统未初始化且相机未启动，重新初始化
                if (!vs.isInitialized() && !vs.isCameraStarted() && binding.blueTextureView.isAvailable) {
                    val surfaceTexture = binding.blueTextureView.surfaceTexture
                    if (surfaceTexture != null) {
                        Log.d(TAG, "HDMI连接: 重新初始化 TpVideoSystem")
                        val previewSurface = Surface(surfaceTexture)
                        vs.initialize(previewSurface)
                    } else {
                        Log.w(TAG, "HDMI连接: SurfaceTexture 不可用")
                    }
                } else {
                    Log.d(TAG, "HDMI连接: TpVideoSystem 已初始化或相机已启动")
                }

                isCameraOpened = true
            }

            if (isSerialConnected) {
                getMainMenuFragment()?.enableAllMenuButtons()
            }
        }
    }

    /**
     * 处理 HDMI 断开事件
     */
    private fun handleHdmiDisconnected() {
        Log.d(TAG, "处理HDMI断开")

        runOnUiThread {
            banner.show("HDMI断开连接", 10000)

            videoSystem?.let { vs ->
                // 如果正在录制，停止录制
                if (vs.isRecording()) {
                    vs.stopRecording()
                }
            }

            isCameraOpened = false
            getMainMenuFragment()?.disableAllMenuButtons()
        }
    }

    private fun showMenu() {
        val menu = MainMenu().apply {
            setRectangleVisibilityListener(this@MainActivity)
        }

        menu.show(supportFragmentManager, "MainMenu")


        if(!isSerialConnected) {
            getMainMenuFragment()?.disableAllMenuButtons()
        }
    }

    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
//        ev?.let {
//            if (it.action == MotionEvent.ACTION_UP) {
//                Log.d(TAG, "dispatchTouchEvent: 2222")
//                showMenu()
//                return true
//            }
//        }
//        return super.dispatchTouchEvent(ev)
        return super.dispatchTouchEvent(ev)
    }

    /**
     * 打开浏览器界面（按照 VideoTest 最佳实践）
     */
    fun showBrowseActivity() {
        Log.d(TAG, "启动浏览器界面")

        val intent = Intent(this, TpVideoBrowse::class.java)
        startActivity(intent)

        Log.d(TAG, "浏览器界面已启动")
    }

    fun SetZoomIn() {
    }

    fun SetZoomOut() {
    }

    // TV 预览功能已集成到 TpVideoSystem 中，移除独立的 TV 管理

    /**
     * 捕获图像（使用 TpVideoSystem 统一管理）
     */
    fun ActivityCaptureImage() {
        // 获取SharedPreferences中的格式设置
        val prefs = getSharedPreferences("CameraSettings", MODE_PRIVATE)
        val format = prefs.getString("image_format", "JPEG") ?: "JPEG"

        // 根据格式设置文件后缀
        val extension = when (format) {
            "JPEG" -> ".jpg"
            "BMP" -> ".bmp"
            "PNG" -> ".png"
            "TIFF" -> ".tiff"
            else -> ".jpg"
        }

        // 创建带正确后缀的输出路径
        val baseName = TpFileManager.createImagePath(this).split(".").dropLast(1).joinToString(".")
        val outputPath = baseName + extension
        Log.d(TAG, "开始捕获图像: $outputPath")

        videoSystem?.let { vs ->
            if (!vs.isInitialized() || !vs.isCameraStarted()) {
                banner.show("相机未就绪，无法捕获图像", 2000)
                return
            }

            vs.captureImage(outputPath)
        } ?: run {
            banner.show("视频系统未初始化", 2000)
        }
    }

    fun initCameraMode() {
        binding.blueTextureView.visibility = View.VISIBLE
        binding.blueTextureView.bringToFront()
//        setupViewListener() //监测状态后打开相机需要一定时间，导致下段开始录像需要延时，逻辑需要优化
        setupRectangleOverlay() // 添加这行
    }

    /**
     * 切换到TV模式（使用 TpVideoSystem 统一管理）
     */
    fun initTVMode() {
        Log.d(TAG, "切换到TV模式")

        videoSystem?.let { vs ->
            // 如果正在录制，先停止录制
            if (vs.isRecording()) {
                stopRecord()
            }

            // 使用 TpVideoSystem 的 TV 模式功能
            try {
                vs.switchToTvMode()

                // 更新UI
                binding.blueTextureView.visibility = View.GONE
                binding.tvView.visibility = View.VISIBLE
                binding.tvView.bringToFront()
                binding.tvView.requestLayout()

                // 禁用ROI功能（TV模式不支持）
                binding.roiView.isEnabled = false
                binding.roiView.visibility = View.INVISIBLE
                binding.tvTimer.bringToFront()

                Log.d(TAG, "已切换到TV模式")
            } catch (e: IllegalStateException) {
                Log.e(TAG, "切换到TV模式失败", e)
                banner.show("切换TV模式失败: ${e.message}", 3000)
            }
        } ?: run {
            banner.show("视频系统未初始化", 2000)
        }
    }

    private fun debugTouchEvent(event: MotionEvent) {
        val action = when (event.actionMasked) {
            MotionEvent.ACTION_DOWN -> "DOWN"
            MotionEvent.ACTION_UP -> "UP"
            MotionEvent.ACTION_MOVE -> "MOVE"
            MotionEvent.ACTION_POINTER_DOWN -> "POINTER_DOWN"
            MotionEvent.ACTION_POINTER_UP -> "POINTER_UP"
            else -> "OTHER: ${event.actionMasked}"
        }

        Log.d("TOUCH_DEBUG", "Action: $action | View: ${binding.rootView.id} | X: ${event.x} Y: ${event.y} | TouchCount: $touchCount")
    }


    private val touchListener = View.OnTouchListener { v, event ->
        debugTouchEvent(event)
        when (event.actionMasked) {
            MotionEvent.ACTION_DOWN -> {
                // 单点触摸开始
                hasPerformedZoom = false
                touchCount = 1
                initialX = event.x
                initialY = event.y
                hasMovedSignificantly = false
                true
            }
            MotionEvent.ACTION_POINTER_DOWN -> {
                // 多点触摸开始
                touchCount = event.pointerCount
                true
            }
            MotionEvent.ACTION_MOVE -> {
                if (touchCount == 1) {
                    // 单点移动，检查移动距离
                    val dx = event.x - initialX
                    val dy = event.y - initialY
                    val distance = Math.sqrt((dx * dx + dy * dy).toDouble()).toFloat()

                    if (distance > MOVE_THRESHOLD) {
                        hasMovedSignificantly = true
                        // 触发平移
                        gestureDetector?.onTouchEvent(event)
                        scaleGestureDetector?.onTouchEvent(event)
                        true
                    } else {
                        false
                    }
                } else if (touchCount > 1) {
                    // 多点触摸，触发缩放
                    val scaleHandled = scaleGestureDetector?.onTouchEvent(event) ?: false
                    if (scaleHandled) {
                        hasPerformedZoom = true
                    }
                    val panHandled = gestureDetector?.onTouchEvent(event) ?: false
                    scaleHandled || panHandled
                } else {
                    false
                }
            }
            MotionEvent.ACTION_UP -> {
                println("###### ACTION_UP ")
                if (touchCount == 1 && !hasMovedSignificantly && !hasPerformedZoom && isMenuEnabled) {
                    // 满足点击条件时调用 performClick()
                    v.performClick()
                    showMenu()
                }
                touchCount = 0
                true
            }
            MotionEvent.ACTION_POINTER_UP -> {
                touchCount = event.pointerCount - 1
                true
            }
            else -> false
        }
    }

    // 在initTouchEvent()中设置监听器

    /**
     * 从TV模式切换回相机模式（使用 TpVideoSystem 统一管理）
     */
    fun TvModeToCameraMode() {
        Log.d(TAG, "从TV模式切换回相机模式")

        videoSystem?.let { vs ->
            try {
                vs.switchToCameraMode()

                // 更新UI
                binding.tvView.visibility = View.GONE
                binding.blueTextureView.visibility = View.VISIBLE
                binding.blueTextureView.bringToFront()

                // 重新设置Surface监听器
                setupViewListener()

                // 恢复ROI功能
                setupRectangleOverlay()

                Log.d(TAG, "已切换回相机模式")
            } catch (e: IllegalStateException) {
                Log.e(TAG, "切换回相机模式失败", e)
                banner.show("切换相机模式失败: ${e.message}", 3000)
            }
        } ?: run {
            banner.show("视频系统未初始化", 2000)
        }
    }

    /**
     * 获取录制状态（使用 TpVideoSystem 统一管理）
     */
    fun isRecording(): Boolean {
        return videoSystem?.isRecording() ?: false
    }

    /**
     * 开始录制（使用 TpVideoSystem 统一管理）
     */
    fun startRecord() {
        videoSystem?.let { vs ->
            if (!vs.isInitialized() || !vs.isCameraStarted()) {
                banner.show("相机未就绪，无法开始录制", 2000)
                return
            }

            if (vs.isRecording()) {
                Log.w(TAG, "已经在录制中")
                return
            }

            val newOutputPath = TpFileManager.createVideoPath(this)
            vs.startRecording(newOutputPath)

            // 启动计时器显示
            startTime = System.currentTimeMillis()
            binding.tvTimer.visibility = View.VISIBLE
            binding.tvTimer.bringToFront()
            startTimer()
        } ?: run {
            banner.show("视频系统未初始化", 2000)
        }
    }

    /**
     * 停止录制（使用 TpVideoSystem 统一管理）
     */
    fun stopRecord() {
        videoSystem?.let { vs ->
            if (vs.isRecording()) {
                vs.stopRecording()
                banner.show("结束录像，正在保存", 20000)
            }
        }

        // 隐藏计时器
        binding.tvTimer.visibility = View.INVISIBLE
    }

    private fun updateMainMenuRecordButton() {
        // 查找并更新MainMenu中的按钮状态
//        supportFragmentManager.findFragmentByTag("MainMenu")?.view?.let { view ->
//            val recordButton = view.findViewById<ImageButton>(R.id.btn_record_video)
//            val resId = if (isRecording) R.drawable.btn_record_video_pressed else R.drawable.btn_record_video_n
//            recordButton.setImageResource(resId)
//        }

    }

    // 移除 initCameraManager() 方法，因为相机管理已集成到 TpVideoSystem 中

    /**
     * 设置 TextureView Surface 监听器（按照 VideoTest 最佳实践）
     */
    private fun setupViewListener() {
        binding.blueTextureView.surfaceTextureListener = object : TextureView.SurfaceTextureListener {
            override fun onSurfaceTextureAvailable(surface: SurfaceTexture, width: Int, height: Int) {
                Log.d(TAG, "Surface 可用，尺寸: ${width}x${height}")

                // 创建用于相机预览的Surface
                val previewSurface = Surface(surface)

                // 检查 TpVideoSystem 状态
                videoSystem?.let { vs ->
                    if (vs.isInitialized()) {
                        Log.w(TAG, "TpVideoSystem 已经初始化，跳过重复初始化")
                        return
                    }

                    Log.d(TAG, "开始初始化 TpVideoSystem")
                    vs.initialize(previewSurface)
                } ?: run {
                    Log.e(TAG, "TpVideoSystem 未在 onCreate 中创建，无法完成初始化")
                }
            }

            override fun onSurfaceTextureSizeChanged(surface: SurfaceTexture, width: Int, height: Int) {
                Log.d(TAG, "Surface 尺寸变化: ${width}x${height}")
            }

            override fun onSurfaceTextureDestroyed(surface: SurfaceTexture): Boolean {
                Log.d(TAG, "Surface 销毁，释放资源")
                // 关键修复：Surface 销毁时必须释放 TpVideoSystem 资源
                releaseResources()
                return true
            }

            override fun onSurfaceTextureUpdated(surface: SurfaceTexture) {
                // Surface 更新时的处理（通常为空）
            }
        }
    }

    // 移除 initVideoEncode() 方法，因为视频编码功能已集成到 TpVideoSystem 中

    /**
     * 释放资源（使用 TpVideoSystem 统一管理）
     */
    private fun releaseResources() {
        Log.d(TAG, "开始释放资源")

        videoSystem?.let { vs ->
            try {
                // 如果正在录制，先停止录制
                if (vs.isRecording()) {
                    Log.d(TAG, "停止录制中...")
                    vs.stopRecording()
                }

                // 释放 TpVideoSystem 资源
                Log.d(TAG, "释放 TpVideoSystem 资源")
                vs.release()

                Log.d(TAG, "资源释放完成")
            }
            catch (e: Exception) {
                Log.e(TAG, "释放资源时发生错误", e)
                banner.show("释放资源失败: ${e.message}", 3000)
            }
        }
    }

    private fun startTimer() {
        updateTimeRunnable = object : Runnable {
            override fun run() {
                val currentTime = System.currentTimeMillis()
                val duration = currentTime - startTime
                updateTimerText(duration)
                handler.postDelayed(this, 1000)
            }
        }
        handler.post(updateTimeRunnable)
    }

    private fun updateTimerText(duration: Long) {
        val seconds = (duration / 1000) % 60
        val minutes = (duration / (1000 * 60)) % 60
        val hours = (duration / (1000 * 60 * 60)) % 24
        binding.tvTimer.text = String.format("%02d:%02d:%02d", hours, minutes, seconds)
    }

    override fun onResume() {
        super.onResume()
        isActivityResumed = true
        Log.d(TAG, "onResume")

        // 根据当前模式恢复预览（按照 VideoTest 模式）
        videoSystem?.let { vs ->
            // 只有在系统未初始化且相机未启动时才重新初始化
            if (!vs.isInitialized() && binding.blueTextureView.isAvailable && !vs.isCameraStarted()) {
                val surfaceTexture = binding.blueTextureView.surfaceTexture
                if (surfaceTexture != null) {
                    Log.d(TAG, "onResume: 重新初始化 TpVideoSystem")
                    val previewSurface = Surface(surfaceTexture)
                    vs.initialize(previewSurface)
                } else {
                    Log.w(TAG, "onResume: SurfaceTexture 不可用")
                }
            } else {
                Log.d(TAG, "onResume: TpVideoSystem 已初始化或相机已启动，跳过重新初始化")
            }
        }

        // 重新启动HDMI服务
        tpHdmiMonitor?.init()
    }

    override fun onPause() {
        super.onPause()
        isActivityResumed = false
        Log.d(TAG, "onPause")

        // 停止录制
        videoSystem?.let { vs ->
            if (vs.isRecording()) {
                vs.stopRecording()
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "onDestroy")

        // 清理ROI状态
        if (binding.roiView.isROIEnabled) {
            binding.roiView.isROIEnabled = false
            Log.d(TAG, "onDestroy - ROI功能已清理")
        }

        // 释放资源（包括TpVideoSystem）
        releaseResources()

        // 停止HDMI服务
        tpHdmiMonitor?.stop()
    }

    fun isActivityForeground(): Boolean {
        return isActivityResumed
    }

    /**
     * 统一的错误处理方法
     */
    private fun handleError(tag: String, message: String, throwable: Throwable? = null) {
        val errorMsg = "[$tag] $message"
        Log.e(TAG, errorMsg, throwable)

        runOnUiThread {
            banner.show(message, 3000)
            Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 检查 TpVideoSystem 状态的统一方法
     */
    private fun checkVideoSystemReady(operationName: String): Boolean {
        val vs = videoSystem
        if (vs == null) {
            handleError("VideoSystem", "视频系统未初始化，无法执行$operationName")
            return false
        }

        if (!vs.isInitialized()) {
            handleError("VideoSystem", "视频系统尚未完成初始化，无法执行$operationName")
            return false
        }

        if (!vs.isCameraStarted()) {
            handleError("VideoSystem", "相机未启动，无法执行$operationName")
            return false
        }

        return true
    }

    private fun initStorageMonitor(){
        TpFileManager.startUsbDriveMonitor(this, object : TpFileManager.StorageListener {
            override fun onUsbDriveConnected(rootPath: String) {
                Toast.makeText(this@MainActivity, "USB存储设备已连接", Toast.LENGTH_SHORT).show()
                createStorageDefaultPath()
            }

            override fun onUsbDriveDisconnected(rootPath: String) {
                Toast.makeText(this@MainActivity, "USB存储设备已断开", Toast.LENGTH_SHORT).show()

            }
        })
    }

    fun createStorageDefaultPath()
    {
        val storagePath = TpFileManager.getExternalStoragePath(this)

        val dcimPath = File(storagePath, getStorageDCIMPath()).path
        val videosPath = File(storagePath, getStorageVideoPath()).path
        val picturesPath = File(storagePath, getStoragePicturePath()).path

        // 创建 DCIM 目录（如果不存在）
        val dcimDir = File(dcimPath)
        if (!dcimDir.exists()) {
            dcimDir.mkdirs()
        }

        // 创建 Videos 目录（如果不存在）
        val videosDir = File(videosPath)
        if (!videosDir.exists()) {
            videosDir.mkdirs()
        }

        // 创建 Pictures 目录（如果不存在）
        val picturesDir = File(picturesPath)
        if (!picturesDir.exists()) {
            picturesDir.mkdirs()
        }
    }


    private fun initScaleGestureDetector() {
        scaleGestureDetector = ScaleGestureDetector(
            this,
            object : ScaleGestureDetector.SimpleOnScaleGestureListener() {
                override fun onScale(detector: ScaleGestureDetector): Boolean {
                    val scaleFactor = detector.scaleFactor
                    val focusX = detector.focusX
                    val focusY = detector.focusY
                    Log.d(TAG, "缩放因子: ${"%.3f".format(scaleFactor)}")

                    // 调用 TpViewTransform 的 applyZoom 方法
                    TpViewTransform.applyZoom(binding.blueTextureView, scaleFactor, focusX, focusY)

                    Log.d(TAG, "onScroll: 开始缩放")
                    return true
                }
            })
    }

    private fun initPanGestureDetector() {
        gestureDetector = GestureDetector(this, object : GestureDetector.SimpleOnGestureListener() {
            override fun onScroll(
                e1: MotionEvent?,
                e2: MotionEvent,
                distanceX: Float,
                distanceY: Float
            ): Boolean {
                TpViewTransform.applyPan(binding.blueTextureView, -distanceX, -distanceY)
                Log.d(TAG, "onScroll: 平移")
                return true
            }

            override fun onDown(e: MotionEvent): Boolean = true
        })
    }

    private fun setupRectangleOverlay() {
        // 确保blueTextureView已经初始化
        binding.blueTextureView.post {
            // 创建FrameLayout作为容器
            val container = FrameLayout(this).apply {
                layoutParams = FrameLayout.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT
                )
            }

            // 将blueTextureView从原有父布局移除并添加到新容器
            (binding.blueTextureView.parent as? ViewGroup)?.removeView(binding.blueTextureView)
            container.addView(binding.blueTextureView)

            // 添加矩形覆盖层
            rectangleOverlayView = TpRectangleOverlayView(this).apply {
                layoutParams = FrameLayout.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT
                )

                // 设置初始位置（居中）
                val width = 200f
                val height = 120f
                val left = (binding.blueTextureView.width - width) / 2
                val top = (binding.blueTextureView.height - height) / 2
                setRectanglePosition(left, top, left + width, top + height)
                //默认隐藏
                visibility = View.GONE

                onPositionChanged = { rect ->
                    Log.d(TAG, "Rectangle position: $rect")
                }
            }
            container.addView(rectangleOverlayView)

            // 将容器添加回原布局
            binding.root.addView(container, 0)
        }
    }

    private fun initTouchEvent() {
        println("########### initTouchEvent() 11")
//        binding.blueTextureView.setOnTouchListener(touchListener)
//        binding.tvView.setOnTouchListener(touchListener)
//
//        // 添加点击监听器确保点击事件触发
//        binding.blueTextureView.setOnClickListener {
//            if (isMenuEnabled) showMenu()
//        }
//
//        binding.tvView.setOnClickListener {
//            if (isMenuEnabled) showMenu()
//        }

        Log.d(TAG, "初始化触摸事件监听器")

        // 创建统一的触摸监听器
        val unifiedTouchListener = View.OnTouchListener { v, event ->
            Log.d("TOUCH_EVENT", "事件发生在视图: ${resources.getResourceName(v.id)}")

            // 处理触摸事件
            val handled = handleTouchEvent(v, event)

            // 如果需要显示菜单，则显示
            if (event.action == MotionEvent.ACTION_UP && canTriggerMenu()) {
                showMenu()
            }

            handled
        }

        // 为两个视图设置相同的监听器
        binding.blueTextureView.setOnTouchListener(unifiedTouchListener)
        binding.tvView.setOnTouchListener(unifiedTouchListener)

        // 确保视图可点击
        binding.blueTextureView.isClickable = true
        binding.tvView.isClickable = true
    }

    override fun onShowRectangle() {
        showRectangleOverlay()
    }

    override fun onHideRectangle() {
        hideRectangleOverlay()
    }

    fun showMeasurementView() {
        binding.measurementView.visibility = View.VISIBLE
    }

    fun hideMeasurementView() {
        binding.measurementView.visibility = View.INVISIBLE
    }

    fun setMeasurementMode(mode: MeasurementOverlayView.Mode) {
        println("###### 1111")
        binding.measurementView.setMode(mode)
    }

    fun clearMeasurements() {
        binding.measurementView.clearAll()
    }

    fun isMeasurementVisible(): Boolean {
        return binding.measurementView.visibility == View.VISIBLE
    }

    fun showRectangleOverlay() {
//        rectangleOverlayView?.visibility = View.VISIBLE
        binding.roiView.isROIEnabled = true
    }

    fun hideRectangleOverlay() {
//        rectangleOverlayView?.visibility = View.GONE
        binding.roiView.isROIEnabled = false
    }

    fun isRectangleVisible(): Boolean {
        return rectangleOverlayView?.visibility == View.VISIBLE
    }

    private fun initISP() {
        TpIspParam.init(this) //初始化isp
        TpIspParam.setOnSerialStateChangedListener { connected ->

            if (connected){
                getMainMenuFragment()?.enableMenuButton()

                if(isHDMIConnected) {
                    getMainMenuFragment()?.enableAllMenuButtons()
                }

                isSerialConnected = true

                banner.show("相机已连接", 2000)

            }else{
                getMainMenuFragment()?.disableMenuButton()
                getMainMenuFragment()?.disableAllMenuButtons()
                if(isRecording){
                    stopRecord()
                }

                isSerialConnected = false
                banner.show("相机断开连接", 1000000000000)
            }
        }
    }

    private fun initCameraMemoryParameter() {
//        val red = TpIspParam.getData(TpIspParam.TOUPTEK_PARAM_WBREDGAIN)
//        TpIspParam.sendToDevice(TpIspParam.TOUPTEK_PARAM_WBREDGAIN, red)
//
//        val green = TpIspParam.getData(TpIspParam.TOUPTEK_PARAM_WBGREENGAIN)
//        TpIspParam.sendToDevice(TpIspParam.TOUPTEK_PARAM_WBREDGAIN, green)
//
//        val sharpness = TpIspParam.getData(TpIspParam.TOUPTEK_PARAM_SHARPNESS)
//        TpIspParam.sendToDevice(TpIspParam.TOUPTEK_PARAM_WBREDGAIN, sharpness)
//
//        Log.d("MainActivity 111","red:${red}, green:${green}, sharpness:${sharpness}")

//        TpIspParam.requestAllParamRanges()
    }

    private fun showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }

    private fun initializeRoiComponents() {
        binding.roiView.setCameraResolution(3840, 2160)

        // 初始化测量视图
        binding.measurementView.apply {
            // 设置默认模式为直线测量
            setMode(MeasurementOverlayView.Mode.LINE)
            // 确保测量视图在ROI视图之上
            bringToFront()
        }
    }



    private fun handleTouchEvent(v: View, event: MotionEvent): Boolean {
        Log.d("#### TouchEvent", "View: ${v.id} | Event: ${MotionEvent.actionToString(event.action)}")
        when (event.actionMasked) {
            MotionEvent.ACTION_DOWN -> {
                // 单点触摸开始
                hasPerformedZoom = false
                touchCount = 1
                initialX = event.x
                initialY = event.y
                hasMovedSignificantly = false
                true
            }
            MotionEvent.ACTION_POINTER_DOWN -> {
                // 多点触摸开始
                touchCount = event.pointerCount
                true
            }
            MotionEvent.ACTION_MOVE -> {
                if (touchCount == 1) {
                    // 单点移动，检查移动距离
                    val dx = event.x - initialX
                    val dy = event.y - initialY
                    val distance = Math.sqrt((dx * dx + dy * dy).toDouble()).toFloat()

                    if (distance > MOVE_THRESHOLD) {
                        hasMovedSignificantly = true
                        // 触发平移
                        gestureDetector?.onTouchEvent(event)
                        scaleGestureDetector?.onTouchEvent(event)
                        return true
                    }
                } else if (touchCount > 1) {
                    // 多点触摸，触发缩放
                    val scaleHandled = scaleGestureDetector?.onTouchEvent(event) ?: false
                    if (scaleHandled) {
                        hasPerformedZoom = true
                    }
                    val panHandled = gestureDetector?.onTouchEvent(event) ?: false
                    return scaleHandled || panHandled
                }
                false
            }
            MotionEvent.ACTION_UP -> {
                if (touchCount == 1 && !hasMovedSignificantly && !hasPerformedZoom && isMenuEnabled) {
                    // 满足点击条件时调用 performClick()
                    v.performClick()
                    showMenu()
                }
                touchCount = 0
                true
            }
            MotionEvent.ACTION_POINTER_UP -> {
                touchCount = event.pointerCount - 1
                true
            }
            else -> false
        }
        return false
    }

    private fun canTriggerMenu(): Boolean {
        return touchCount == 1 &&
                !hasMovedSignificantly &&
                !hasPerformedZoom &&
                isMenuEnabled
    }


    private fun takeScreenshot() {
        val rootView = window.decorView.rootView
        rootView.isDrawingCacheEnabled = true
        val screenshotBitmap = Bitmap.createBitmap(rootView.drawingCache)
        rootView.isDrawingCacheEnabled = false

        // 保存截图
        saveBitmapToStorage(screenshotBitmap)
    }

    /**
     * 保存Bitmap到外部存储
     */
    private fun saveBitmapToStorage(bitmap: Bitmap) {
        val fileName = "screenshot_${System.currentTimeMillis()}.png"
//        val picturesDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES)
        val picturesDir = "/storage/2627-B03B0/DCIM/Images/"
        val screenshotFile = File(picturesDir, fileName)

        try {
            FileOutputStream(screenshotFile).use { fos ->
                bitmap.compress(Bitmap.CompressFormat.PNG, 100, fos)
                Toast.makeText(this, "截图已保存: ${screenshotFile.path}", Toast.LENGTH_LONG).show()
            }
        } catch (e: Exception) {
            Log.e("Screenshot", "保存截图失败", e)
            Toast.makeText(this, "保存截图失败", Toast.LENGTH_SHORT).show()
        }
    }

    fun captureDialogFragment(dialogFragment: DialogFragment): Bitmap? {
        val dialog = dialogFragment.dialog
        if (dialog == null) {
            Log.e("DialogCapture", "Dialog is null")
            return null
        }

        // 获取对话框的窗口视图
        val decorView = dialog.window?.decorView
        if (decorView == null) {
            Log.e("DialogCapture", "DecorView is null")
            return null
        }

        // 确保视图已经正确测量和布局
        decorView.measure(
            View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED),
            View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
        )

        decorView.layout(0, 0, decorView.measuredWidth, decorView.measuredHeight)

        // 创建位图并绘制视图
        val bitmap = Bitmap.createBitmap(
            decorView.measuredWidth,
            decorView.measuredHeight,
            Bitmap.Config.ARGB_8888
        )

        val canvas = Canvas(bitmap)
        decorView.draw(canvas)

        return bitmap
    }

    fun captureImage(outputPath: String) {
        try {
            videoSystem?.captureImage(outputPath)
        } catch (e: Exception) {
            // Handle the exception, for example by logging it or showing an error message
            println("Error capturing image: " + e.message)
        }
    }

}
