{"buildFiles": ["E:\\work\\Andriod\\yolo_demo\\opencv410\\libcxx_helper\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\work\\Andriod\\yolo_demo\\opencv410\\.cxx\\Debug\\3n2w4716\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\work\\Andriod\\yolo_demo\\opencv410\\.cxx\\Debug\\3n2w4716\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"opencv_jni_shared::@6890427a1f51a3e7e1df": {"artifactName": "opencv_jni_shared", "abi": "arm64-v8a", "output": "E:\\work\\Andriod\\yolo_demo\\opencv410\\.cxx\\Debug\\3n2w4716\\arm64-v8a\\libopencv_jni_shared.a", "runtimeFiles": []}}}