C/C++ Build Metadata                A           c         N                                rC:\Users\<USER>\AppData\Local\Android\Sdk\ndk\26.1.10909125\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++.exe   '--target=armv7-none-linux-androideabi24   t--sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot   -g   	-DANDROID   -fdata-sections   -ffunction-sections   -funwind-tables   -fstack-protector-strong   -no-canonical-prefixes   -D_FORTIFY_SOURCE=2   -march=armv7-a   -mthumb   -Wformat   -Werror=format-security   -fno-limit-debug-info   -fPIC   CE:\work\Andriod\yolo_demo\opencv410\.cxx\Debug\3n2w4716\armeabi-v7a   opencv_jni_shared   ;E:\work\Andriod\yolo_demo\opencv410\libcxx_helper\dummy.cpp   ,CMakeFiles\opencv_jni_shared.dir\dummy.cpp.o                              	   
         
            