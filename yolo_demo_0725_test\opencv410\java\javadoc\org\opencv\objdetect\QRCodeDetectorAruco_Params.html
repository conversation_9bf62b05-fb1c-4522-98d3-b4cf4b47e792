<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_392) on Sun Jun 02 16:58:25 UTC 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>QRCodeDetectorAruco_Params (OpenCV 4.10.0 Java documentation)</title>
<meta name="date" content="2024-06-02">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="QRCodeDetectorAruco_Params (OpenCV 4.10.0 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/objdetect/QRCodeDetectorAruco.html" title="class in org.opencv.objdetect"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/objdetect/QRCodeEncoder.html" title="class in org.opencv.objdetect"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/objdetect/QRCodeDetectorAruco_Params.html" target="_top">Frames</a></li>
<li><a href="QRCodeDetectorAruco_Params.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.objdetect</div>
<h2 title="Class QRCodeDetectorAruco_Params" class="title">Class QRCodeDetectorAruco_Params</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.opencv.objdetect.QRCodeDetectorAruco_Params</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">QRCodeDetectorAruco_Params</span>
extends java.lang.Object</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/QRCodeDetectorAruco_Params.html#QRCodeDetectorAruco_Params--">QRCodeDetectorAruco_Params</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/objdetect/QRCodeDetectorAruco_Params.html" title="class in org.opencv.objdetect">QRCodeDetectorAruco_Params</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/QRCodeDetectorAruco_Params.html#Z:Z__fromPtr__-long-">__fromPtr__</a></span>(long&nbsp;addr)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/QRCodeDetectorAruco_Params.html#get_maxColorsMismatch--">get_maxColorsMismatch</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/QRCodeDetectorAruco_Params.html#get_maxModuleSizeMismatch--">get_maxModuleSizeMismatch</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/QRCodeDetectorAruco_Params.html#get_maxPenalties--">get_maxPenalties</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/QRCodeDetectorAruco_Params.html#get_maxRotation--">get_maxRotation</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/QRCodeDetectorAruco_Params.html#get_maxTimingPatternMismatch--">get_maxTimingPatternMismatch</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/QRCodeDetectorAruco_Params.html#get_minModuleSizeInPyramid--">get_minModuleSizeInPyramid</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>float</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/QRCodeDetectorAruco_Params.html#get_scaleTimingPatternScore--">get_scaleTimingPatternScore</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/QRCodeDetectorAruco_Params.html#getNativeObjAddr--">getNativeObjAddr</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/QRCodeDetectorAruco_Params.html#set_maxColorsMismatch-float-">set_maxColorsMismatch</a></span>(float&nbsp;maxColorsMismatch)</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/QRCodeDetectorAruco_Params.html#set_maxModuleSizeMismatch-float-">set_maxModuleSizeMismatch</a></span>(float&nbsp;maxModuleSizeMismatch)</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/QRCodeDetectorAruco_Params.html#set_maxPenalties-float-">set_maxPenalties</a></span>(float&nbsp;maxPenalties)</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/QRCodeDetectorAruco_Params.html#set_maxRotation-float-">set_maxRotation</a></span>(float&nbsp;maxRotation)</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/QRCodeDetectorAruco_Params.html#set_maxTimingPatternMismatch-float-">set_maxTimingPatternMismatch</a></span>(float&nbsp;maxTimingPatternMismatch)</code>&nbsp;</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/QRCodeDetectorAruco_Params.html#set_minModuleSizeInPyramid-float-">set_minModuleSizeInPyramid</a></span>(float&nbsp;minModuleSizeInPyramid)</code>&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/objdetect/QRCodeDetectorAruco_Params.html#set_scaleTimingPatternScore-float-">set_scaleTimingPatternScore</a></span>(float&nbsp;scaleTimingPatternScore)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="QRCodeDetectorAruco_Params--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>QRCodeDetectorAruco_Params</h4>
<pre>public&nbsp;QRCodeDetectorAruco_Params()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="Z:Z__fromPtr__-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>__fromPtr__</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/objdetect/QRCodeDetectorAruco_Params.html" title="class in org.opencv.objdetect">QRCodeDetectorAruco_Params</a>&nbsp;__fromPtr__(long&nbsp;addr)</pre>
</li>
</ul>
<a name="get_maxColorsMismatch--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_maxColorsMismatch</h4>
<pre>public&nbsp;float&nbsp;get_maxColorsMismatch()</pre>
</li>
</ul>
<a name="get_maxModuleSizeMismatch--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_maxModuleSizeMismatch</h4>
<pre>public&nbsp;float&nbsp;get_maxModuleSizeMismatch()</pre>
</li>
</ul>
<a name="get_maxPenalties--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_maxPenalties</h4>
<pre>public&nbsp;float&nbsp;get_maxPenalties()</pre>
</li>
</ul>
<a name="get_maxRotation--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_maxRotation</h4>
<pre>public&nbsp;float&nbsp;get_maxRotation()</pre>
</li>
</ul>
<a name="get_maxTimingPatternMismatch--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_maxTimingPatternMismatch</h4>
<pre>public&nbsp;float&nbsp;get_maxTimingPatternMismatch()</pre>
</li>
</ul>
<a name="get_minModuleSizeInPyramid--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_minModuleSizeInPyramid</h4>
<pre>public&nbsp;float&nbsp;get_minModuleSizeInPyramid()</pre>
</li>
</ul>
<a name="get_scaleTimingPatternScore--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_scaleTimingPatternScore</h4>
<pre>public&nbsp;float&nbsp;get_scaleTimingPatternScore()</pre>
</li>
</ul>
<a name="getNativeObjAddr--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNativeObjAddr</h4>
<pre>public&nbsp;long&nbsp;getNativeObjAddr()</pre>
</li>
</ul>
<a name="set_maxColorsMismatch-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_maxColorsMismatch</h4>
<pre>public&nbsp;void&nbsp;set_maxColorsMismatch(float&nbsp;maxColorsMismatch)</pre>
</li>
</ul>
<a name="set_maxModuleSizeMismatch-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_maxModuleSizeMismatch</h4>
<pre>public&nbsp;void&nbsp;set_maxModuleSizeMismatch(float&nbsp;maxModuleSizeMismatch)</pre>
</li>
</ul>
<a name="set_maxPenalties-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_maxPenalties</h4>
<pre>public&nbsp;void&nbsp;set_maxPenalties(float&nbsp;maxPenalties)</pre>
</li>
</ul>
<a name="set_maxRotation-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_maxRotation</h4>
<pre>public&nbsp;void&nbsp;set_maxRotation(float&nbsp;maxRotation)</pre>
</li>
</ul>
<a name="set_maxTimingPatternMismatch-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_maxTimingPatternMismatch</h4>
<pre>public&nbsp;void&nbsp;set_maxTimingPatternMismatch(float&nbsp;maxTimingPatternMismatch)</pre>
</li>
</ul>
<a name="set_minModuleSizeInPyramid-float-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_minModuleSizeInPyramid</h4>
<pre>public&nbsp;void&nbsp;set_minModuleSizeInPyramid(float&nbsp;minModuleSizeInPyramid)</pre>
</li>
</ul>
<a name="set_scaleTimingPatternScore-float-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>set_scaleTimingPatternScore</h4>
<pre>public&nbsp;void&nbsp;set_scaleTimingPatternScore(float&nbsp;scaleTimingPatternScore)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/objdetect/QRCodeDetectorAruco.html" title="class in org.opencv.objdetect"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/objdetect/QRCodeEncoder.html" title="class in org.opencv.objdetect"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/objdetect/QRCodeDetectorAruco_Params.html" target="_top">Frames</a></li>
<li><a href="QRCodeDetectorAruco_Params.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2024-06-02 16:58:23 / OpenCV 4.10.0</small></p>
</body>
</html>
