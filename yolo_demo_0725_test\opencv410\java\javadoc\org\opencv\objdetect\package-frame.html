<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_392) on Sun Jun 02 16:58:28 UTC 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>org.opencv.objdetect (OpenCV 4.10.0 Java documentation)</title>
<meta name="date" content="2024-06-02">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<h1 class="bar"><a href="../../../org/opencv/objdetect/package-summary.html" target="classFrame">org.opencv.objdetect</a></h1>
<div class="indexContainer">
<h2 title="Classes">Classes</h2>
<ul title="Classes">
<li><a href="ArucoDetector.html" title="class in org.opencv.objdetect" target="classFrame">ArucoDetector</a></li>
<li><a href="BarcodeDetector.html" title="class in org.opencv.objdetect" target="classFrame">BarcodeDetector</a></li>
<li><a href="BaseCascadeClassifier.html" title="class in org.opencv.objdetect" target="classFrame">BaseCascadeClassifier</a></li>
<li><a href="Board.html" title="class in org.opencv.objdetect" target="classFrame">Board</a></li>
<li><a href="CascadeClassifier.html" title="class in org.opencv.objdetect" target="classFrame">CascadeClassifier</a></li>
<li><a href="CharucoBoard.html" title="class in org.opencv.objdetect" target="classFrame">CharucoBoard</a></li>
<li><a href="CharucoDetector.html" title="class in org.opencv.objdetect" target="classFrame">CharucoDetector</a></li>
<li><a href="CharucoParameters.html" title="class in org.opencv.objdetect" target="classFrame">CharucoParameters</a></li>
<li><a href="DetectorParameters.html" title="class in org.opencv.objdetect" target="classFrame">DetectorParameters</a></li>
<li><a href="Dictionary.html" title="class in org.opencv.objdetect" target="classFrame">Dictionary</a></li>
<li><a href="FaceDetectorYN.html" title="class in org.opencv.objdetect" target="classFrame">FaceDetectorYN</a></li>
<li><a href="FaceRecognizerSF.html" title="class in org.opencv.objdetect" target="classFrame">FaceRecognizerSF</a></li>
<li><a href="GraphicalCodeDetector.html" title="class in org.opencv.objdetect" target="classFrame">GraphicalCodeDetector</a></li>
<li><a href="GridBoard.html" title="class in org.opencv.objdetect" target="classFrame">GridBoard</a></li>
<li><a href="HOGDescriptor.html" title="class in org.opencv.objdetect" target="classFrame">HOGDescriptor</a></li>
<li><a href="Objdetect.html" title="class in org.opencv.objdetect" target="classFrame">Objdetect</a></li>
<li><a href="QRCodeDetector.html" title="class in org.opencv.objdetect" target="classFrame">QRCodeDetector</a></li>
<li><a href="QRCodeDetectorAruco.html" title="class in org.opencv.objdetect" target="classFrame">QRCodeDetectorAruco</a></li>
<li><a href="QRCodeDetectorAruco_Params.html" title="class in org.opencv.objdetect" target="classFrame">QRCodeDetectorAruco_Params</a></li>
<li><a href="QRCodeEncoder.html" title="class in org.opencv.objdetect" target="classFrame">QRCodeEncoder</a></li>
<li><a href="QRCodeEncoder_Params.html" title="class in org.opencv.objdetect" target="classFrame">QRCodeEncoder_Params</a></li>
<li><a href="RefineParameters.html" title="class in org.opencv.objdetect" target="classFrame">RefineParameters</a></li>
</ul>
</div>
</body>
</html>
