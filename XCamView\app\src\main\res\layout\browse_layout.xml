<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@android:color/white">

    <FrameLayout
        android:id="@+id/right_panel"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="#F5F5F5"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintWidth_percent="0.2"> <!-- 占25%宽度 -->

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:gravity="center"
            android:text="操作面板"
            android:textSize="18sp"/>
    </FrameLayout>

    <LinearLayout
        android:id="@+id/toolbar"
        android:layout_width="0dp"
        android:layout_height="60dp"
        android:background="#FFFFFF"
        android:orientation="horizontal"
        android:padding="4dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toEndOf="@id/right_panel"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- 左侧按钮组：前两个按钮 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="2"
            android:orientation="horizontal">


        </LinearLayout>

        <!-- 占位空间：将右侧按钮推到最右边 -->
        <Space
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="24" />

        <!-- 右侧按钮组：后三个按钮 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="3"
            android:orientation="horizontal"
            android:background="#FFFFFF">
            <!-- 添加全选按钮 -->
            <TextView
                android:id="@+id/browse_select_all"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="#FFFFFF"
                android:gravity="center"
                android:text="全选"
                android:textSize="16sp"
                android:textColor="#000000"
                android:clickable="true"
                android:focusable="true"
                android:padding="4dp"/>

            <!-- 添加删除按钮 -->
            <TextView
                android:id="@+id/browse_delete"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="#FFFFFF"
                android:gravity="center"
                android:text="删除"
                android:textSize="16sp"
                android:textColor="#000000"
                android:clickable="true"
                android:focusable="true"
                android:padding="6dp"/>

        </LinearLayout>
        <ImageView
            android:id="@+id/browse_config"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:padding="4dp"
            android:scaleType="centerInside"
            android:src="@drawable/ic_menu"
            android:background="#FFFFFF"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginEnd="16dp"/>

    </LinearLayout>

    <FrameLayout
        android:id="@+id/recycler_container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="#F0FFFF"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        app:layout_constraintStart_toEndOf="@id/right_panel"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:paddingBottom="40dp" />

    </FrameLayout>

    <!-- 底部操作按钮栏 -->
    <LinearLayout
        android:id="@+id/bottom_action_bar"
        android:layout_width="0dp"
        android:layout_height="100dp"
        android:orientation="horizontal"
        android:background="@drawable/bottom_panel_background"
        android:visibility="gone"
        android:paddingTop="12dp"
        android:paddingBottom="16dp"
        android:paddingStart="12dp"
        android:paddingEnd="12dp"
        android:elevation="8dp"
        android:outlineProvider="background"
        android:layout_marginBottom="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintWidth_percent="0.5">



        <!-- 按钮1 -->
        <LinearLayout
            android:id="@+id/btn_container1"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:clickable="true"
            android:focusable="true"
            android:background="?attr/selectableItemBackground"
            android:padding="6dp">
            <ImageView
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:src="@drawable/ic_cancel"
                android:clickable="false"
                android:focusable="false"/>
            <TextView
                android:id="@+id/btn_action1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="取消"
                android:textSize="14sp"
                android:textColor="#FF000000"
                android:layout_marginTop="6dp"
                android:layout_marginBottom="4dp"
                android:clickable="false"
                android:focusable="false"/>
        </LinearLayout>

        <!-- 按钮2 -->
        <LinearLayout
            android:id="@+id/btn_container2"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:clickable="true"
            android:focusable="true"
            android:background="?attr/selectableItemBackground"
            android:padding="6dp">
            <ImageView
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:src="@drawable/ic_allselect"
                android:clickable="false"
                android:focusable="false"/>
            <TextView
                android:id="@+id/btn_action2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="全选"
                android:textSize="14sp"
                android:textColor="#FF000000"
                android:layout_marginTop="6dp"
                android:layout_marginBottom="4dp"
                android:clickable="false"
                android:focusable="false"/>
        </LinearLayout>

        <!-- 按钮3 -->
        <LinearLayout
            android:id="@+id/btn_container3"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:clickable="true"
            android:focusable="true"
            android:background="?attr/selectableItemBackground"
            android:padding="6dp">
            <ImageView
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:src="@drawable/ic_action3"
                android:clickable="false"
                android:focusable="false"/>
            <TextView
                android:id="@+id/btn_action3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="操作3"
                android:textSize="14sp"
                android:textColor="#FF000000"
                android:layout_marginTop="6dp"
                android:layout_marginBottom="4dp"
                android:clickable="false"
                android:focusable="false"/>
        </LinearLayout>

        <!-- 按钮4 -->
        <LinearLayout
            android:id="@+id/btn_container4"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:clickable="true"
            android:focusable="true"
            android:background="?attr/selectableItemBackground"
            android:padding="6dp">
            <ImageView
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:src="@drawable/ic_delete_n"
                android:clickable="false"
                android:focusable="false"/>
            <TextView
                android:id="@+id/btn_action4"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="删除"
                android:textSize="14sp"
                android:textColor="#FF000000"
                android:layout_marginTop="6dp"
                android:layout_marginBottom="4dp"
                android:clickable="false"
                android:focusable="false"/>
        </LinearLayout>

        <!-- 按钮5 -->
        <LinearLayout
            android:id="@+id/btn_container5"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:clickable="true"
            android:focusable="true"
            android:background="?attr/selectableItemBackground"
            android:padding="6dp">
            <ImageView
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:src="@drawable/ic_menu"
                android:clickable="false"
                android:focusable="false"/>
            <TextView
                android:id="@+id/btn_action5"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="更多"
                android:textSize="14sp"
                android:textColor="#FF000000"
                android:layout_marginTop="6dp"
                android:layout_marginBottom="4dp"
                android:clickable="false"
                android:focusable="false"/>
        </LinearLayout>
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>