1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="org.opencv.test" >
4
5    <uses-sdk
5-->E:\work\Andriod\yolo_demo\opencv410\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest9505896281976167784.xml:5:5-74
6        android:minSdkVersion="24"
6-->E:\work\Andriod\yolo_demo\opencv410\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest9505896281976167784.xml:5:15-41
7        android:targetSdkVersion="35" />
7-->E:\work\Andriod\yolo_demo\opencv410\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest9505896281976167784.xml:5:42-71
8
9    <instrumentation
9-->E:\work\Andriod\yolo_demo\opencv410\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest9505896281976167784.xml:11:5-15:65
10        android:name="android.test.InstrumentationTestRunner"
10-->E:\work\Andriod\yolo_demo\opencv410\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest9505896281976167784.xml:11:22-75
11        android:functionalTest="false"
11-->E:\work\Andriod\yolo_demo\opencv410\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest9505896281976167784.xml:14:22-52
12        android:handleProfiling="false"
12-->E:\work\Andriod\yolo_demo\opencv410\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest9505896281976167784.xml:13:22-53
13        android:label="Tests for org.opencv.test"
13-->E:\work\Andriod\yolo_demo\opencv410\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest9505896281976167784.xml:15:22-63
14        android:targetPackage="org.opencv.test" />
14-->E:\work\Andriod\yolo_demo\opencv410\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest9505896281976167784.xml:12:22-61
15
16    <application
16-->E:\work\Andriod\yolo_demo\opencv410\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest9505896281976167784.xml:7:5-9:19
17        android:debuggable="true"
18        android:extractNativeLibs="false" >
19        <uses-library android:name="android.test.runner" />
19-->E:\work\Andriod\yolo_demo\opencv410\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest9505896281976167784.xml:8:9-60
19-->E:\work\Andriod\yolo_demo\opencv410\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest9505896281976167784.xml:8:23-57
20    </application>
21
22</manifest>
