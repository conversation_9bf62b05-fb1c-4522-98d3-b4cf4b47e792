<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_392) on Sun Jun 02 16:58:27 UTC 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>StereoSGBM (OpenCV 4.10.0 Java documentation)</title>
<meta name="date" content="2024-06-02">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="StereoSGBM (OpenCV 4.10.0 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9,"i9":9,"i10":9,"i11":9,"i12":9,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/calib3d/StereoMatcher.html" title="class in org.opencv.calib3d"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/calib3d/UsacParams.html" title="class in org.opencv.calib3d"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/calib3d/StereoSGBM.html" target="_top">Frames</a></li>
<li><a href="StereoSGBM.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.calib3d</div>
<h2 title="Class StereoSGBM" class="title">Class StereoSGBM</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">org.opencv.core.Algorithm</a></li>
<li>
<ul class="inheritance">
<li><a href="../../../org/opencv/calib3d/StereoMatcher.html" title="class in org.opencv.calib3d">org.opencv.calib3d.StereoMatcher</a></li>
<li>
<ul class="inheritance">
<li>org.opencv.calib3d.StereoSGBM</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">StereoSGBM</span>
extends <a href="../../../org/opencv/calib3d/StereoMatcher.html" title="class in org.opencv.calib3d">StereoMatcher</a></pre>
<div class="block">The class implements the modified H. Hirschmuller algorithm CITE: HH08 that differs from the original
 one as follows:

 <ul>
   <li>
    By default, the algorithm is single-pass, which means that you consider only 5 directions
 instead of 8. Set mode=StereoSGBM::MODE_HH in createStereoSGBM to run the full variant of the
 algorithm but beware that it may consume a lot of memory.
   </li>
   <li>
    The algorithm matches blocks, not individual pixels. Though, setting blockSize=1 reduces the
 blocks to single pixels.
   </li>
   <li>
    Mutual information cost function is not implemented. Instead, a simpler Birchfield-Tomasi
 sub-pixel metric from CITE: BT98 is used. Though, the color images are supported as well.
   </li>
   <li>
    Some pre- and post- processing steps from K. Konolige algorithm StereoBM are included, for
 example: pre-filtering (StereoBM::PREFILTER_XSOBEL type) and post-filtering (uniqueness
 check, quadratic interpolation and speckle filtering).
   </li>
 </ul>

 <b>Note:</b>
 <ul>
   <li>
       (Python) An example illustrating the use of the StereoSGBM matching algorithm can be found
         at opencv_source_code/samples/python/stereo_match.py
   </li>
 </ul></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoSGBM.html#MODE_HH">MODE_HH</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoSGBM.html#MODE_HH4">MODE_HH4</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoSGBM.html#MODE_SGBM">MODE_SGBM</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoSGBM.html#MODE_SGBM_3WAY">MODE_SGBM_3WAY</a></span></code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.org.opencv.calib3d.StereoMatcher">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;org.opencv.calib3d.<a href="../../../org/opencv/calib3d/StereoMatcher.html" title="class in org.opencv.calib3d">StereoMatcher</a></h3>
<code><a href="../../../org/opencv/calib3d/StereoMatcher.html#DISP_SCALE">DISP_SCALE</a>, <a href="../../../org/opencv/calib3d/StereoMatcher.html#DISP_SHIFT">DISP_SHIFT</a></code></li>
</ul>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/calib3d/StereoSGBM.html" title="class in org.opencv.calib3d">StereoSGBM</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoSGBM.html#Z:Z__fromPtr__-long-">__fromPtr__</a></span>(long&nbsp;addr)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/calib3d/StereoSGBM.html" title="class in org.opencv.calib3d">StereoSGBM</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoSGBM.html#create--">create</a></span>()</code>
<div class="block">Creates StereoSGBM object

     rectification algorithms can shift images, so this parameter needs to be adjusted accordingly.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/calib3d/StereoSGBM.html" title="class in org.opencv.calib3d">StereoSGBM</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoSGBM.html#create-int-">create</a></span>(int&nbsp;minDisparity)</code>
<div class="block">Creates StereoSGBM object</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/calib3d/StereoSGBM.html" title="class in org.opencv.calib3d">StereoSGBM</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoSGBM.html#create-int-int-">create</a></span>(int&nbsp;minDisparity,
      int&nbsp;numDisparities)</code>
<div class="block">Creates StereoSGBM object</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/calib3d/StereoSGBM.html" title="class in org.opencv.calib3d">StereoSGBM</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoSGBM.html#create-int-int-int-">create</a></span>(int&nbsp;minDisparity,
      int&nbsp;numDisparities,
      int&nbsp;blockSize)</code>
<div class="block">Creates StereoSGBM object</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/calib3d/StereoSGBM.html" title="class in org.opencv.calib3d">StereoSGBM</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoSGBM.html#create-int-int-int-int-">create</a></span>(int&nbsp;minDisparity,
      int&nbsp;numDisparities,
      int&nbsp;blockSize,
      int&nbsp;P1)</code>
<div class="block">Creates StereoSGBM object</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/calib3d/StereoSGBM.html" title="class in org.opencv.calib3d">StereoSGBM</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoSGBM.html#create-int-int-int-int-int-">create</a></span>(int&nbsp;minDisparity,
      int&nbsp;numDisparities,
      int&nbsp;blockSize,
      int&nbsp;P1,
      int&nbsp;P2)</code>
<div class="block">Creates StereoSGBM object</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/calib3d/StereoSGBM.html" title="class in org.opencv.calib3d">StereoSGBM</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoSGBM.html#create-int-int-int-int-int-int-">create</a></span>(int&nbsp;minDisparity,
      int&nbsp;numDisparities,
      int&nbsp;blockSize,
      int&nbsp;P1,
      int&nbsp;P2,
      int&nbsp;disp12MaxDiff)</code>
<div class="block">Creates StereoSGBM object</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/calib3d/StereoSGBM.html" title="class in org.opencv.calib3d">StereoSGBM</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoSGBM.html#create-int-int-int-int-int-int-int-">create</a></span>(int&nbsp;minDisparity,
      int&nbsp;numDisparities,
      int&nbsp;blockSize,
      int&nbsp;P1,
      int&nbsp;P2,
      int&nbsp;disp12MaxDiff,
      int&nbsp;preFilterCap)</code>
<div class="block">Creates StereoSGBM object</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/calib3d/StereoSGBM.html" title="class in org.opencv.calib3d">StereoSGBM</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoSGBM.html#create-int-int-int-int-int-int-int-int-">create</a></span>(int&nbsp;minDisparity,
      int&nbsp;numDisparities,
      int&nbsp;blockSize,
      int&nbsp;P1,
      int&nbsp;P2,
      int&nbsp;disp12MaxDiff,
      int&nbsp;preFilterCap,
      int&nbsp;uniquenessRatio)</code>
<div class="block">Creates StereoSGBM object</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/calib3d/StereoSGBM.html" title="class in org.opencv.calib3d">StereoSGBM</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoSGBM.html#create-int-int-int-int-int-int-int-int-int-">create</a></span>(int&nbsp;minDisparity,
      int&nbsp;numDisparities,
      int&nbsp;blockSize,
      int&nbsp;P1,
      int&nbsp;P2,
      int&nbsp;disp12MaxDiff,
      int&nbsp;preFilterCap,
      int&nbsp;uniquenessRatio,
      int&nbsp;speckleWindowSize)</code>
<div class="block">Creates StereoSGBM object</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/calib3d/StereoSGBM.html" title="class in org.opencv.calib3d">StereoSGBM</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoSGBM.html#create-int-int-int-int-int-int-int-int-int-int-">create</a></span>(int&nbsp;minDisparity,
      int&nbsp;numDisparities,
      int&nbsp;blockSize,
      int&nbsp;P1,
      int&nbsp;P2,
      int&nbsp;disp12MaxDiff,
      int&nbsp;preFilterCap,
      int&nbsp;uniquenessRatio,
      int&nbsp;speckleWindowSize,
      int&nbsp;speckleRange)</code>
<div class="block">Creates StereoSGBM object</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/calib3d/StereoSGBM.html" title="class in org.opencv.calib3d">StereoSGBM</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoSGBM.html#create-int-int-int-int-int-int-int-int-int-int-int-">create</a></span>(int&nbsp;minDisparity,
      int&nbsp;numDisparities,
      int&nbsp;blockSize,
      int&nbsp;P1,
      int&nbsp;P2,
      int&nbsp;disp12MaxDiff,
      int&nbsp;preFilterCap,
      int&nbsp;uniquenessRatio,
      int&nbsp;speckleWindowSize,
      int&nbsp;speckleRange,
      int&nbsp;mode)</code>
<div class="block">Creates StereoSGBM object</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoSGBM.html#getMode--">getMode</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoSGBM.html#getP1--">getP1</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoSGBM.html#getP2--">getP2</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoSGBM.html#getPreFilterCap--">getPreFilterCap</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoSGBM.html#getUniquenessRatio--">getUniquenessRatio</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoSGBM.html#setMode-int-">setMode</a></span>(int&nbsp;mode)</code>&nbsp;</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoSGBM.html#setP1-int-">setP1</a></span>(int&nbsp;P1)</code>&nbsp;</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoSGBM.html#setP2-int-">setP2</a></span>(int&nbsp;P2)</code>&nbsp;</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoSGBM.html#setPreFilterCap-int-">setPreFilterCap</a></span>(int&nbsp;preFilterCap)</code>&nbsp;</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/StereoSGBM.html#setUniquenessRatio-int-">setUniquenessRatio</a></span>(int&nbsp;uniquenessRatio)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.calib3d.StereoMatcher">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.calib3d.<a href="../../../org/opencv/calib3d/StereoMatcher.html" title="class in org.opencv.calib3d">StereoMatcher</a></h3>
<code><a href="../../../org/opencv/calib3d/StereoMatcher.html#compute-org.opencv.core.Mat-org.opencv.core.Mat-org.opencv.core.Mat-">compute</a>, <a href="../../../org/opencv/calib3d/StereoMatcher.html#getBlockSize--">getBlockSize</a>, <a href="../../../org/opencv/calib3d/StereoMatcher.html#getDisp12MaxDiff--">getDisp12MaxDiff</a>, <a href="../../../org/opencv/calib3d/StereoMatcher.html#getMinDisparity--">getMinDisparity</a>, <a href="../../../org/opencv/calib3d/StereoMatcher.html#getNumDisparities--">getNumDisparities</a>, <a href="../../../org/opencv/calib3d/StereoMatcher.html#getSpeckleRange--">getSpeckleRange</a>, <a href="../../../org/opencv/calib3d/StereoMatcher.html#getSpeckleWindowSize--">getSpeckleWindowSize</a>, <a href="../../../org/opencv/calib3d/StereoMatcher.html#setBlockSize-int-">setBlockSize</a>, <a href="../../../org/opencv/calib3d/StereoMatcher.html#setDisp12MaxDiff-int-">setDisp12MaxDiff</a>, <a href="../../../org/opencv/calib3d/StereoMatcher.html#setMinDisparity-int-">setMinDisparity</a>, <a href="../../../org/opencv/calib3d/StereoMatcher.html#setNumDisparities-int-">setNumDisparities</a>, <a href="../../../org/opencv/calib3d/StereoMatcher.html#setSpeckleRange-int-">setSpeckleRange</a>, <a href="../../../org/opencv/calib3d/StereoMatcher.html#setSpeckleWindowSize-int-">setSpeckleWindowSize</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.opencv.core.Algorithm">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.opencv.core.<a href="../../../org/opencv/core/Algorithm.html" title="class in org.opencv.core">Algorithm</a></h3>
<code><a href="../../../org/opencv/core/Algorithm.html#clear--">clear</a>, <a href="../../../org/opencv/core/Algorithm.html#empty--">empty</a>, <a href="../../../org/opencv/core/Algorithm.html#getDefaultName--">getDefaultName</a>, <a href="../../../org/opencv/core/Algorithm.html#getNativeObjAddr--">getNativeObjAddr</a>, <a href="../../../org/opencv/core/Algorithm.html#save-java.lang.String-">save</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="MODE_HH">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MODE_HH</h4>
<pre>public static final&nbsp;int MODE_HH</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.calib3d.StereoSGBM.MODE_HH">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MODE_HH4">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MODE_HH4</h4>
<pre>public static final&nbsp;int MODE_HH4</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.calib3d.StereoSGBM.MODE_HH4">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MODE_SGBM">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>MODE_SGBM</h4>
<pre>public static final&nbsp;int MODE_SGBM</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.calib3d.StereoSGBM.MODE_SGBM">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="MODE_SGBM_3WAY">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>MODE_SGBM_3WAY</h4>
<pre>public static final&nbsp;int MODE_SGBM_3WAY</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../constant-values.html#org.opencv.calib3d.StereoSGBM.MODE_SGBM_3WAY">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="Z:Z__fromPtr__-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>__fromPtr__</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/calib3d/StereoSGBM.html" title="class in org.opencv.calib3d">StereoSGBM</a>&nbsp;__fromPtr__(long&nbsp;addr)</pre>
</li>
</ul>
<a name="create--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/calib3d/StereoSGBM.html" title="class in org.opencv.calib3d">StereoSGBM</a>&nbsp;create()</pre>
<div class="block">Creates StereoSGBM object

     rectification algorithms can shift images, so this parameter needs to be adjusted accordingly.
     zero. In the current implementation, this parameter must be divisible by 16.
     somewhere in the 3..11 range.
     the smoother the disparity is. P1 is the penalty on the disparity change by plus or minus 1
     between neighbor pixels. P2 is the penalty on the disparity change by more than 1 between neighbor
     pixels. The algorithm requires P2 &gt; P1 . See stereo_match.cpp sample where some reasonably good
     P1 and P2 values are shown (like 8\*number_of_image_channels\*blockSize\*blockSize and
     32\*number_of_image_channels\*blockSize\*blockSize , respectively).
     disparity check. Set it to a non-positive value to disable the check.
     computes x-derivative at each pixel and clips its value by [-preFilterCap, preFilterCap] interval.
     The result values are passed to the Birchfield-Tomasi pixel cost function.
     value should "win" the second best value to consider the found match correct. Normally, a value
     within the 5-15 range is good enough.
     and invalidate. Set it to 0 to disable speckle filtering. Otherwise, set it somewhere in the
     50-200 range.
     filtering, set the parameter to a positive value, it will be implicitly multiplied by 16.
     Normally, 1 or 2 is good enough.
     algorithm. It will consume O(W\*H\*numDisparities) bytes, which is large for 640x480 stereo and
     huge for HD-size pictures. By default, it is set to false .

     The first constructor initializes StereoSGBM with all the default parameters. So, you only have to
     set StereoSGBM::numDisparities at minimum. The second constructor enables you to set each parameter
     to a custom value.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/calib3d/StereoSGBM.html" title="class in org.opencv.calib3d">StereoSGBM</a>&nbsp;create(int&nbsp;minDisparity)</pre>
<div class="block">Creates StereoSGBM object</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>minDisparity</code> - Minimum possible disparity value. Normally, it is zero but sometimes
     rectification algorithms can shift images, so this parameter needs to be adjusted accordingly.
     zero. In the current implementation, this parameter must be divisible by 16.
     somewhere in the 3..11 range.
     the smoother the disparity is. P1 is the penalty on the disparity change by plus or minus 1
     between neighbor pixels. P2 is the penalty on the disparity change by more than 1 between neighbor
     pixels. The algorithm requires P2 &gt; P1 . See stereo_match.cpp sample where some reasonably good
     P1 and P2 values are shown (like 8\*number_of_image_channels\*blockSize\*blockSize and
     32\*number_of_image_channels\*blockSize\*blockSize , respectively).
     disparity check. Set it to a non-positive value to disable the check.
     computes x-derivative at each pixel and clips its value by [-preFilterCap, preFilterCap] interval.
     The result values are passed to the Birchfield-Tomasi pixel cost function.
     value should "win" the second best value to consider the found match correct. Normally, a value
     within the 5-15 range is good enough.
     and invalidate. Set it to 0 to disable speckle filtering. Otherwise, set it somewhere in the
     50-200 range.
     filtering, set the parameter to a positive value, it will be implicitly multiplied by 16.
     Normally, 1 or 2 is good enough.
     algorithm. It will consume O(W\*H\*numDisparities) bytes, which is large for 640x480 stereo and
     huge for HD-size pictures. By default, it is set to false .

     The first constructor initializes StereoSGBM with all the default parameters. So, you only have to
     set StereoSGBM::numDisparities at minimum. The second constructor enables you to set each parameter
     to a custom value.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/calib3d/StereoSGBM.html" title="class in org.opencv.calib3d">StereoSGBM</a>&nbsp;create(int&nbsp;minDisparity,
                                int&nbsp;numDisparities)</pre>
<div class="block">Creates StereoSGBM object</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>minDisparity</code> - Minimum possible disparity value. Normally, it is zero but sometimes
     rectification algorithms can shift images, so this parameter needs to be adjusted accordingly.</dd>
<dd><code>numDisparities</code> - Maximum disparity minus minimum disparity. The value is always greater than
     zero. In the current implementation, this parameter must be divisible by 16.
     somewhere in the 3..11 range.
     the smoother the disparity is. P1 is the penalty on the disparity change by plus or minus 1
     between neighbor pixels. P2 is the penalty on the disparity change by more than 1 between neighbor
     pixels. The algorithm requires P2 &gt; P1 . See stereo_match.cpp sample where some reasonably good
     P1 and P2 values are shown (like 8\*number_of_image_channels\*blockSize\*blockSize and
     32\*number_of_image_channels\*blockSize\*blockSize , respectively).
     disparity check. Set it to a non-positive value to disable the check.
     computes x-derivative at each pixel and clips its value by [-preFilterCap, preFilterCap] interval.
     The result values are passed to the Birchfield-Tomasi pixel cost function.
     value should "win" the second best value to consider the found match correct. Normally, a value
     within the 5-15 range is good enough.
     and invalidate. Set it to 0 to disable speckle filtering. Otherwise, set it somewhere in the
     50-200 range.
     filtering, set the parameter to a positive value, it will be implicitly multiplied by 16.
     Normally, 1 or 2 is good enough.
     algorithm. It will consume O(W\*H\*numDisparities) bytes, which is large for 640x480 stereo and
     huge for HD-size pictures. By default, it is set to false .

     The first constructor initializes StereoSGBM with all the default parameters. So, you only have to
     set StereoSGBM::numDisparities at minimum. The second constructor enables you to set each parameter
     to a custom value.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/calib3d/StereoSGBM.html" title="class in org.opencv.calib3d">StereoSGBM</a>&nbsp;create(int&nbsp;minDisparity,
                                int&nbsp;numDisparities,
                                int&nbsp;blockSize)</pre>
<div class="block">Creates StereoSGBM object</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>minDisparity</code> - Minimum possible disparity value. Normally, it is zero but sometimes
     rectification algorithms can shift images, so this parameter needs to be adjusted accordingly.</dd>
<dd><code>numDisparities</code> - Maximum disparity minus minimum disparity. The value is always greater than
     zero. In the current implementation, this parameter must be divisible by 16.</dd>
<dd><code>blockSize</code> - Matched block size. It must be an odd number &gt;=1 . Normally, it should be
     somewhere in the 3..11 range.
     the smoother the disparity is. P1 is the penalty on the disparity change by plus or minus 1
     between neighbor pixels. P2 is the penalty on the disparity change by more than 1 between neighbor
     pixels. The algorithm requires P2 &gt; P1 . See stereo_match.cpp sample where some reasonably good
     P1 and P2 values are shown (like 8\*number_of_image_channels\*blockSize\*blockSize and
     32\*number_of_image_channels\*blockSize\*blockSize , respectively).
     disparity check. Set it to a non-positive value to disable the check.
     computes x-derivative at each pixel and clips its value by [-preFilterCap, preFilterCap] interval.
     The result values are passed to the Birchfield-Tomasi pixel cost function.
     value should "win" the second best value to consider the found match correct. Normally, a value
     within the 5-15 range is good enough.
     and invalidate. Set it to 0 to disable speckle filtering. Otherwise, set it somewhere in the
     50-200 range.
     filtering, set the parameter to a positive value, it will be implicitly multiplied by 16.
     Normally, 1 or 2 is good enough.
     algorithm. It will consume O(W\*H\*numDisparities) bytes, which is large for 640x480 stereo and
     huge for HD-size pictures. By default, it is set to false .

     The first constructor initializes StereoSGBM with all the default parameters. So, you only have to
     set StereoSGBM::numDisparities at minimum. The second constructor enables you to set each parameter
     to a custom value.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-int-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/calib3d/StereoSGBM.html" title="class in org.opencv.calib3d">StereoSGBM</a>&nbsp;create(int&nbsp;minDisparity,
                                int&nbsp;numDisparities,
                                int&nbsp;blockSize,
                                int&nbsp;P1)</pre>
<div class="block">Creates StereoSGBM object</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>minDisparity</code> - Minimum possible disparity value. Normally, it is zero but sometimes
     rectification algorithms can shift images, so this parameter needs to be adjusted accordingly.</dd>
<dd><code>numDisparities</code> - Maximum disparity minus minimum disparity. The value is always greater than
     zero. In the current implementation, this parameter must be divisible by 16.</dd>
<dd><code>blockSize</code> - Matched block size. It must be an odd number &gt;=1 . Normally, it should be
     somewhere in the 3..11 range.</dd>
<dd><code>P1</code> - The first parameter controlling the disparity smoothness. See below.
     the smoother the disparity is. P1 is the penalty on the disparity change by plus or minus 1
     between neighbor pixels. P2 is the penalty on the disparity change by more than 1 between neighbor
     pixels. The algorithm requires P2 &gt; P1 . See stereo_match.cpp sample where some reasonably good
     P1 and P2 values are shown (like 8\*number_of_image_channels\*blockSize\*blockSize and
     32\*number_of_image_channels\*blockSize\*blockSize , respectively).
     disparity check. Set it to a non-positive value to disable the check.
     computes x-derivative at each pixel and clips its value by [-preFilterCap, preFilterCap] interval.
     The result values are passed to the Birchfield-Tomasi pixel cost function.
     value should "win" the second best value to consider the found match correct. Normally, a value
     within the 5-15 range is good enough.
     and invalidate. Set it to 0 to disable speckle filtering. Otherwise, set it somewhere in the
     50-200 range.
     filtering, set the parameter to a positive value, it will be implicitly multiplied by 16.
     Normally, 1 or 2 is good enough.
     algorithm. It will consume O(W\*H\*numDisparities) bytes, which is large for 640x480 stereo and
     huge for HD-size pictures. By default, it is set to false .

     The first constructor initializes StereoSGBM with all the default parameters. So, you only have to
     set StereoSGBM::numDisparities at minimum. The second constructor enables you to set each parameter
     to a custom value.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-int-int-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/calib3d/StereoSGBM.html" title="class in org.opencv.calib3d">StereoSGBM</a>&nbsp;create(int&nbsp;minDisparity,
                                int&nbsp;numDisparities,
                                int&nbsp;blockSize,
                                int&nbsp;P1,
                                int&nbsp;P2)</pre>
<div class="block">Creates StereoSGBM object</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>minDisparity</code> - Minimum possible disparity value. Normally, it is zero but sometimes
     rectification algorithms can shift images, so this parameter needs to be adjusted accordingly.</dd>
<dd><code>numDisparities</code> - Maximum disparity minus minimum disparity. The value is always greater than
     zero. In the current implementation, this parameter must be divisible by 16.</dd>
<dd><code>blockSize</code> - Matched block size. It must be an odd number &gt;=1 . Normally, it should be
     somewhere in the 3..11 range.</dd>
<dd><code>P1</code> - The first parameter controlling the disparity smoothness. See below.</dd>
<dd><code>P2</code> - The second parameter controlling the disparity smoothness. The larger the values are,
     the smoother the disparity is. P1 is the penalty on the disparity change by plus or minus 1
     between neighbor pixels. P2 is the penalty on the disparity change by more than 1 between neighbor
     pixels. The algorithm requires P2 &gt; P1 . See stereo_match.cpp sample where some reasonably good
     P1 and P2 values are shown (like 8\*number_of_image_channels\*blockSize\*blockSize and
     32\*number_of_image_channels\*blockSize\*blockSize , respectively).
     disparity check. Set it to a non-positive value to disable the check.
     computes x-derivative at each pixel and clips its value by [-preFilterCap, preFilterCap] interval.
     The result values are passed to the Birchfield-Tomasi pixel cost function.
     value should "win" the second best value to consider the found match correct. Normally, a value
     within the 5-15 range is good enough.
     and invalidate. Set it to 0 to disable speckle filtering. Otherwise, set it somewhere in the
     50-200 range.
     filtering, set the parameter to a positive value, it will be implicitly multiplied by 16.
     Normally, 1 or 2 is good enough.
     algorithm. It will consume O(W\*H\*numDisparities) bytes, which is large for 640x480 stereo and
     huge for HD-size pictures. By default, it is set to false .

     The first constructor initializes StereoSGBM with all the default parameters. So, you only have to
     set StereoSGBM::numDisparities at minimum. The second constructor enables you to set each parameter
     to a custom value.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-int-int-int-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/calib3d/StereoSGBM.html" title="class in org.opencv.calib3d">StereoSGBM</a>&nbsp;create(int&nbsp;minDisparity,
                                int&nbsp;numDisparities,
                                int&nbsp;blockSize,
                                int&nbsp;P1,
                                int&nbsp;P2,
                                int&nbsp;disp12MaxDiff)</pre>
<div class="block">Creates StereoSGBM object</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>minDisparity</code> - Minimum possible disparity value. Normally, it is zero but sometimes
     rectification algorithms can shift images, so this parameter needs to be adjusted accordingly.</dd>
<dd><code>numDisparities</code> - Maximum disparity minus minimum disparity. The value is always greater than
     zero. In the current implementation, this parameter must be divisible by 16.</dd>
<dd><code>blockSize</code> - Matched block size. It must be an odd number &gt;=1 . Normally, it should be
     somewhere in the 3..11 range.</dd>
<dd><code>P1</code> - The first parameter controlling the disparity smoothness. See below.</dd>
<dd><code>P2</code> - The second parameter controlling the disparity smoothness. The larger the values are,
     the smoother the disparity is. P1 is the penalty on the disparity change by plus or minus 1
     between neighbor pixels. P2 is the penalty on the disparity change by more than 1 between neighbor
     pixels. The algorithm requires P2 &gt; P1 . See stereo_match.cpp sample where some reasonably good
     P1 and P2 values are shown (like 8\*number_of_image_channels\*blockSize\*blockSize and
     32\*number_of_image_channels\*blockSize\*blockSize , respectively).</dd>
<dd><code>disp12MaxDiff</code> - Maximum allowed difference (in integer pixel units) in the left-right
     disparity check. Set it to a non-positive value to disable the check.
     computes x-derivative at each pixel and clips its value by [-preFilterCap, preFilterCap] interval.
     The result values are passed to the Birchfield-Tomasi pixel cost function.
     value should "win" the second best value to consider the found match correct. Normally, a value
     within the 5-15 range is good enough.
     and invalidate. Set it to 0 to disable speckle filtering. Otherwise, set it somewhere in the
     50-200 range.
     filtering, set the parameter to a positive value, it will be implicitly multiplied by 16.
     Normally, 1 or 2 is good enough.
     algorithm. It will consume O(W\*H\*numDisparities) bytes, which is large for 640x480 stereo and
     huge for HD-size pictures. By default, it is set to false .

     The first constructor initializes StereoSGBM with all the default parameters. So, you only have to
     set StereoSGBM::numDisparities at minimum. The second constructor enables you to set each parameter
     to a custom value.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-int-int-int-int-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/calib3d/StereoSGBM.html" title="class in org.opencv.calib3d">StereoSGBM</a>&nbsp;create(int&nbsp;minDisparity,
                                int&nbsp;numDisparities,
                                int&nbsp;blockSize,
                                int&nbsp;P1,
                                int&nbsp;P2,
                                int&nbsp;disp12MaxDiff,
                                int&nbsp;preFilterCap)</pre>
<div class="block">Creates StereoSGBM object</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>minDisparity</code> - Minimum possible disparity value. Normally, it is zero but sometimes
     rectification algorithms can shift images, so this parameter needs to be adjusted accordingly.</dd>
<dd><code>numDisparities</code> - Maximum disparity minus minimum disparity. The value is always greater than
     zero. In the current implementation, this parameter must be divisible by 16.</dd>
<dd><code>blockSize</code> - Matched block size. It must be an odd number &gt;=1 . Normally, it should be
     somewhere in the 3..11 range.</dd>
<dd><code>P1</code> - The first parameter controlling the disparity smoothness. See below.</dd>
<dd><code>P2</code> - The second parameter controlling the disparity smoothness. The larger the values are,
     the smoother the disparity is. P1 is the penalty on the disparity change by plus or minus 1
     between neighbor pixels. P2 is the penalty on the disparity change by more than 1 between neighbor
     pixels. The algorithm requires P2 &gt; P1 . See stereo_match.cpp sample where some reasonably good
     P1 and P2 values are shown (like 8\*number_of_image_channels\*blockSize\*blockSize and
     32\*number_of_image_channels\*blockSize\*blockSize , respectively).</dd>
<dd><code>disp12MaxDiff</code> - Maximum allowed difference (in integer pixel units) in the left-right
     disparity check. Set it to a non-positive value to disable the check.</dd>
<dd><code>preFilterCap</code> - Truncation value for the prefiltered image pixels. The algorithm first
     computes x-derivative at each pixel and clips its value by [-preFilterCap, preFilterCap] interval.
     The result values are passed to the Birchfield-Tomasi pixel cost function.
     value should "win" the second best value to consider the found match correct. Normally, a value
     within the 5-15 range is good enough.
     and invalidate. Set it to 0 to disable speckle filtering. Otherwise, set it somewhere in the
     50-200 range.
     filtering, set the parameter to a positive value, it will be implicitly multiplied by 16.
     Normally, 1 or 2 is good enough.
     algorithm. It will consume O(W\*H\*numDisparities) bytes, which is large for 640x480 stereo and
     huge for HD-size pictures. By default, it is set to false .

     The first constructor initializes StereoSGBM with all the default parameters. So, you only have to
     set StereoSGBM::numDisparities at minimum. The second constructor enables you to set each parameter
     to a custom value.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-int-int-int-int-int-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/calib3d/StereoSGBM.html" title="class in org.opencv.calib3d">StereoSGBM</a>&nbsp;create(int&nbsp;minDisparity,
                                int&nbsp;numDisparities,
                                int&nbsp;blockSize,
                                int&nbsp;P1,
                                int&nbsp;P2,
                                int&nbsp;disp12MaxDiff,
                                int&nbsp;preFilterCap,
                                int&nbsp;uniquenessRatio)</pre>
<div class="block">Creates StereoSGBM object</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>minDisparity</code> - Minimum possible disparity value. Normally, it is zero but sometimes
     rectification algorithms can shift images, so this parameter needs to be adjusted accordingly.</dd>
<dd><code>numDisparities</code> - Maximum disparity minus minimum disparity. The value is always greater than
     zero. In the current implementation, this parameter must be divisible by 16.</dd>
<dd><code>blockSize</code> - Matched block size. It must be an odd number &gt;=1 . Normally, it should be
     somewhere in the 3..11 range.</dd>
<dd><code>P1</code> - The first parameter controlling the disparity smoothness. See below.</dd>
<dd><code>P2</code> - The second parameter controlling the disparity smoothness. The larger the values are,
     the smoother the disparity is. P1 is the penalty on the disparity change by plus or minus 1
     between neighbor pixels. P2 is the penalty on the disparity change by more than 1 between neighbor
     pixels. The algorithm requires P2 &gt; P1 . See stereo_match.cpp sample where some reasonably good
     P1 and P2 values are shown (like 8\*number_of_image_channels\*blockSize\*blockSize and
     32\*number_of_image_channels\*blockSize\*blockSize , respectively).</dd>
<dd><code>disp12MaxDiff</code> - Maximum allowed difference (in integer pixel units) in the left-right
     disparity check. Set it to a non-positive value to disable the check.</dd>
<dd><code>preFilterCap</code> - Truncation value for the prefiltered image pixels. The algorithm first
     computes x-derivative at each pixel and clips its value by [-preFilterCap, preFilterCap] interval.
     The result values are passed to the Birchfield-Tomasi pixel cost function.</dd>
<dd><code>uniquenessRatio</code> - Margin in percentage by which the best (minimum) computed cost function
     value should "win" the second best value to consider the found match correct. Normally, a value
     within the 5-15 range is good enough.
     and invalidate. Set it to 0 to disable speckle filtering. Otherwise, set it somewhere in the
     50-200 range.
     filtering, set the parameter to a positive value, it will be implicitly multiplied by 16.
     Normally, 1 or 2 is good enough.
     algorithm. It will consume O(W\*H\*numDisparities) bytes, which is large for 640x480 stereo and
     huge for HD-size pictures. By default, it is set to false .

     The first constructor initializes StereoSGBM with all the default parameters. So, you only have to
     set StereoSGBM::numDisparities at minimum. The second constructor enables you to set each parameter
     to a custom value.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-int-int-int-int-int-int-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/calib3d/StereoSGBM.html" title="class in org.opencv.calib3d">StereoSGBM</a>&nbsp;create(int&nbsp;minDisparity,
                                int&nbsp;numDisparities,
                                int&nbsp;blockSize,
                                int&nbsp;P1,
                                int&nbsp;P2,
                                int&nbsp;disp12MaxDiff,
                                int&nbsp;preFilterCap,
                                int&nbsp;uniquenessRatio,
                                int&nbsp;speckleWindowSize)</pre>
<div class="block">Creates StereoSGBM object</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>minDisparity</code> - Minimum possible disparity value. Normally, it is zero but sometimes
     rectification algorithms can shift images, so this parameter needs to be adjusted accordingly.</dd>
<dd><code>numDisparities</code> - Maximum disparity minus minimum disparity. The value is always greater than
     zero. In the current implementation, this parameter must be divisible by 16.</dd>
<dd><code>blockSize</code> - Matched block size. It must be an odd number &gt;=1 . Normally, it should be
     somewhere in the 3..11 range.</dd>
<dd><code>P1</code> - The first parameter controlling the disparity smoothness. See below.</dd>
<dd><code>P2</code> - The second parameter controlling the disparity smoothness. The larger the values are,
     the smoother the disparity is. P1 is the penalty on the disparity change by plus or minus 1
     between neighbor pixels. P2 is the penalty on the disparity change by more than 1 between neighbor
     pixels. The algorithm requires P2 &gt; P1 . See stereo_match.cpp sample where some reasonably good
     P1 and P2 values are shown (like 8\*number_of_image_channels\*blockSize\*blockSize and
     32\*number_of_image_channels\*blockSize\*blockSize , respectively).</dd>
<dd><code>disp12MaxDiff</code> - Maximum allowed difference (in integer pixel units) in the left-right
     disparity check. Set it to a non-positive value to disable the check.</dd>
<dd><code>preFilterCap</code> - Truncation value for the prefiltered image pixels. The algorithm first
     computes x-derivative at each pixel and clips its value by [-preFilterCap, preFilterCap] interval.
     The result values are passed to the Birchfield-Tomasi pixel cost function.</dd>
<dd><code>uniquenessRatio</code> - Margin in percentage by which the best (minimum) computed cost function
     value should "win" the second best value to consider the found match correct. Normally, a value
     within the 5-15 range is good enough.</dd>
<dd><code>speckleWindowSize</code> - Maximum size of smooth disparity regions to consider their noise speckles
     and invalidate. Set it to 0 to disable speckle filtering. Otherwise, set it somewhere in the
     50-200 range.
     filtering, set the parameter to a positive value, it will be implicitly multiplied by 16.
     Normally, 1 or 2 is good enough.
     algorithm. It will consume O(W\*H\*numDisparities) bytes, which is large for 640x480 stereo and
     huge for HD-size pictures. By default, it is set to false .

     The first constructor initializes StereoSGBM with all the default parameters. So, you only have to
     set StereoSGBM::numDisparities at minimum. The second constructor enables you to set each parameter
     to a custom value.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-int-int-int-int-int-int-int-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/calib3d/StereoSGBM.html" title="class in org.opencv.calib3d">StereoSGBM</a>&nbsp;create(int&nbsp;minDisparity,
                                int&nbsp;numDisparities,
                                int&nbsp;blockSize,
                                int&nbsp;P1,
                                int&nbsp;P2,
                                int&nbsp;disp12MaxDiff,
                                int&nbsp;preFilterCap,
                                int&nbsp;uniquenessRatio,
                                int&nbsp;speckleWindowSize,
                                int&nbsp;speckleRange)</pre>
<div class="block">Creates StereoSGBM object</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>minDisparity</code> - Minimum possible disparity value. Normally, it is zero but sometimes
     rectification algorithms can shift images, so this parameter needs to be adjusted accordingly.</dd>
<dd><code>numDisparities</code> - Maximum disparity minus minimum disparity. The value is always greater than
     zero. In the current implementation, this parameter must be divisible by 16.</dd>
<dd><code>blockSize</code> - Matched block size. It must be an odd number &gt;=1 . Normally, it should be
     somewhere in the 3..11 range.</dd>
<dd><code>P1</code> - The first parameter controlling the disparity smoothness. See below.</dd>
<dd><code>P2</code> - The second parameter controlling the disparity smoothness. The larger the values are,
     the smoother the disparity is. P1 is the penalty on the disparity change by plus or minus 1
     between neighbor pixels. P2 is the penalty on the disparity change by more than 1 between neighbor
     pixels. The algorithm requires P2 &gt; P1 . See stereo_match.cpp sample where some reasonably good
     P1 and P2 values are shown (like 8\*number_of_image_channels\*blockSize\*blockSize and
     32\*number_of_image_channels\*blockSize\*blockSize , respectively).</dd>
<dd><code>disp12MaxDiff</code> - Maximum allowed difference (in integer pixel units) in the left-right
     disparity check. Set it to a non-positive value to disable the check.</dd>
<dd><code>preFilterCap</code> - Truncation value for the prefiltered image pixels. The algorithm first
     computes x-derivative at each pixel and clips its value by [-preFilterCap, preFilterCap] interval.
     The result values are passed to the Birchfield-Tomasi pixel cost function.</dd>
<dd><code>uniquenessRatio</code> - Margin in percentage by which the best (minimum) computed cost function
     value should "win" the second best value to consider the found match correct. Normally, a value
     within the 5-15 range is good enough.</dd>
<dd><code>speckleWindowSize</code> - Maximum size of smooth disparity regions to consider their noise speckles
     and invalidate. Set it to 0 to disable speckle filtering. Otherwise, set it somewhere in the
     50-200 range.</dd>
<dd><code>speckleRange</code> - Maximum disparity variation within each connected component. If you do speckle
     filtering, set the parameter to a positive value, it will be implicitly multiplied by 16.
     Normally, 1 or 2 is good enough.
     algorithm. It will consume O(W\*H\*numDisparities) bytes, which is large for 640x480 stereo and
     huge for HD-size pictures. By default, it is set to false .

     The first constructor initializes StereoSGBM with all the default parameters. So, you only have to
     set StereoSGBM::numDisparities at minimum. The second constructor enables you to set each parameter
     to a custom value.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="create-int-int-int-int-int-int-int-int-int-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>create</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/calib3d/StereoSGBM.html" title="class in org.opencv.calib3d">StereoSGBM</a>&nbsp;create(int&nbsp;minDisparity,
                                int&nbsp;numDisparities,
                                int&nbsp;blockSize,
                                int&nbsp;P1,
                                int&nbsp;P2,
                                int&nbsp;disp12MaxDiff,
                                int&nbsp;preFilterCap,
                                int&nbsp;uniquenessRatio,
                                int&nbsp;speckleWindowSize,
                                int&nbsp;speckleRange,
                                int&nbsp;mode)</pre>
<div class="block">Creates StereoSGBM object</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>minDisparity</code> - Minimum possible disparity value. Normally, it is zero but sometimes
     rectification algorithms can shift images, so this parameter needs to be adjusted accordingly.</dd>
<dd><code>numDisparities</code> - Maximum disparity minus minimum disparity. The value is always greater than
     zero. In the current implementation, this parameter must be divisible by 16.</dd>
<dd><code>blockSize</code> - Matched block size. It must be an odd number &gt;=1 . Normally, it should be
     somewhere in the 3..11 range.</dd>
<dd><code>P1</code> - The first parameter controlling the disparity smoothness. See below.</dd>
<dd><code>P2</code> - The second parameter controlling the disparity smoothness. The larger the values are,
     the smoother the disparity is. P1 is the penalty on the disparity change by plus or minus 1
     between neighbor pixels. P2 is the penalty on the disparity change by more than 1 between neighbor
     pixels. The algorithm requires P2 &gt; P1 . See stereo_match.cpp sample where some reasonably good
     P1 and P2 values are shown (like 8\*number_of_image_channels\*blockSize\*blockSize and
     32\*number_of_image_channels\*blockSize\*blockSize , respectively).</dd>
<dd><code>disp12MaxDiff</code> - Maximum allowed difference (in integer pixel units) in the left-right
     disparity check. Set it to a non-positive value to disable the check.</dd>
<dd><code>preFilterCap</code> - Truncation value for the prefiltered image pixels. The algorithm first
     computes x-derivative at each pixel and clips its value by [-preFilterCap, preFilterCap] interval.
     The result values are passed to the Birchfield-Tomasi pixel cost function.</dd>
<dd><code>uniquenessRatio</code> - Margin in percentage by which the best (minimum) computed cost function
     value should "win" the second best value to consider the found match correct. Normally, a value
     within the 5-15 range is good enough.</dd>
<dd><code>speckleWindowSize</code> - Maximum size of smooth disparity regions to consider their noise speckles
     and invalidate. Set it to 0 to disable speckle filtering. Otherwise, set it somewhere in the
     50-200 range.</dd>
<dd><code>speckleRange</code> - Maximum disparity variation within each connected component. If you do speckle
     filtering, set the parameter to a positive value, it will be implicitly multiplied by 16.
     Normally, 1 or 2 is good enough.</dd>
<dd><code>mode</code> - Set it to StereoSGBM::MODE_HH to run the full-scale two-pass dynamic programming
     algorithm. It will consume O(W\*H\*numDisparities) bytes, which is large for 640x480 stereo and
     huge for HD-size pictures. By default, it is set to false .

     The first constructor initializes StereoSGBM with all the default parameters. So, you only have to
     set StereoSGBM::numDisparities at minimum. The second constructor enables you to set each parameter
     to a custom value.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>automatically generated</dd>
</dl>
</li>
</ul>
<a name="getMode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMode</h4>
<pre>public&nbsp;int&nbsp;getMode()</pre>
</li>
</ul>
<a name="getP1--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getP1</h4>
<pre>public&nbsp;int&nbsp;getP1()</pre>
</li>
</ul>
<a name="getP2--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getP2</h4>
<pre>public&nbsp;int&nbsp;getP2()</pre>
</li>
</ul>
<a name="getPreFilterCap--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPreFilterCap</h4>
<pre>public&nbsp;int&nbsp;getPreFilterCap()</pre>
</li>
</ul>
<a name="getUniquenessRatio--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUniquenessRatio</h4>
<pre>public&nbsp;int&nbsp;getUniquenessRatio()</pre>
</li>
</ul>
<a name="setMode-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMode</h4>
<pre>public&nbsp;void&nbsp;setMode(int&nbsp;mode)</pre>
</li>
</ul>
<a name="setP1-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setP1</h4>
<pre>public&nbsp;void&nbsp;setP1(int&nbsp;P1)</pre>
</li>
</ul>
<a name="setP2-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setP2</h4>
<pre>public&nbsp;void&nbsp;setP2(int&nbsp;P2)</pre>
</li>
</ul>
<a name="setPreFilterCap-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPreFilterCap</h4>
<pre>public&nbsp;void&nbsp;setPreFilterCap(int&nbsp;preFilterCap)</pre>
</li>
</ul>
<a name="setUniquenessRatio-int-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setUniquenessRatio</h4>
<pre>public&nbsp;void&nbsp;setUniquenessRatio(int&nbsp;uniquenessRatio)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/calib3d/StereoMatcher.html" title="class in org.opencv.calib3d"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/opencv/calib3d/UsacParams.html" title="class in org.opencv.calib3d"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/calib3d/StereoSGBM.html" target="_top">Frames</a></li>
<li><a href="StereoSGBM.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2024-06-02 16:58:23 / OpenCV 4.10.0</small></p>
</body>
</html>
