package com.touptek.xcamview.activity.settings

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.Fragment
import com.touptek.xcamview.R

class TpFormatSettingsFragment : Fragment() {

    companion object {
        private const val TAG = "FormatSettings"
    }

    private lateinit var saveModeGroup: RadioGroup
    private lateinit var measurementMarksCheck: CheckBox
    private lateinit var measurementValuesCheck: CheckBox
    private lateinit var measurementReportCheck: CheckBox
    private lateinit var labelStyleSpinner: Spinner
    private lateinit var fontSizeSpinner: Spinner
    private lateinit var stylePreviewButton: Button
    private lateinit var resetSettingsButton: Button
    private lateinit var colorSpaceGroup: RadioGroup
    private lateinit var colorDepthGroup: RadioGroup
    private lateinit var compressionSpinner: Spinner
    private lateinit var fusionCheck: CheckBox
    private lateinit var layerCheck: CheckBox

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_format_settings, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupFormatSelection()
        loadSavedSettings()
        setupMeasurementSettings()
        setupAdvancedSettings()
    }

    private fun setupFormatSelection() {
        view?.findViewById<RadioGroup>(R.id.format_group)?.setOnCheckedChangeListener { _, checkedId ->
            when (checkedId) {
                R.id.radio_jpeg -> handleFormatSelected("JPEG")
                R.id.radio_bmp -> handleFormatSelected("BMP")
                R.id.radio_png -> handleFormatSelected("PNG")
                R.id.radio_tiff -> handleFormatSelected("TIFF")
            }

            // 根据格式启用/禁用质量调节
            updateQualityControlState()
        }
    }

    private fun updateQualityControlState() {
        val prefs = requireContext().getSharedPreferences("CameraSettings", Context.MODE_PRIVATE)
        val format = prefs.getString("image_format", "JPEG") ?: "JPEG"
    }

    private fun loadSavedSettings() {
        val prefs = requireContext().getSharedPreferences("CameraSettings", Context.MODE_PRIVATE)

        // 加载分辨率设置
        val resolution = prefs.getString("image_resolution", "1920x1080")

        // 加载格式设置
        val format = prefs.getString("image_format", "JPEG")
        val formatId = when (format) {
            "JPEG" -> R.id.radio_jpeg
            "BMP" -> R.id.radio_bmp
            "PNG" -> R.id.radio_png
            "TIFF" -> R.id.radio_tiff
            else -> R.id.radio_jpeg
        }
        view?.findViewById<RadioGroup>(R.id.format_group)?.check(formatId)
    }

    private fun saveResolution(resolution: String) {
        val prefs = requireContext().getSharedPreferences("CameraSettings", Context.MODE_PRIVATE)
        prefs.edit().putString("image_resolution", resolution).apply()
        Toast.makeText(requireContext(), "分辨率设置为 $resolution", Toast.LENGTH_SHORT).show()
    }

    private fun handleFormatSelected(format: String) {
        val prefs = requireContext().getSharedPreferences("CameraSettings", Context.MODE_PRIVATE)
        prefs.edit().putString("image_format", format).apply()
        Toast.makeText(requireContext(), "已设置为${format}格式", Toast.LENGTH_SHORT).show()
    }

    private fun saveQualitySetting(quality: Int) {
        val prefs = requireContext().getSharedPreferences("CameraSettings", Context.MODE_PRIVATE)
        prefs.edit().putInt("image_quality", quality).apply()
        Toast.makeText(requireContext(), "图像质量设置为 $quality%", Toast.LENGTH_SHORT).show()
    }

    private fun setupMeasurementSettings() {
        // 初始化新增控件
        saveModeGroup = requireView().findViewById(R.id.save_mode_group)
        measurementMarksCheck = requireView().findViewById(R.id.check_save_measurement_marks)
        measurementValuesCheck = requireView().findViewById(R.id.check_save_measurement_values)
        measurementReportCheck = requireView().findViewById(R.id.check_save_measurement_report)
        labelStyleSpinner = requireView().findViewById(R.id.spinner_label_style)
        fontSizeSpinner = requireView().findViewById(R.id.spinner_font_size)
        stylePreviewButton = requireView().findViewById(R.id.btn_style_preview)
        resetSettingsButton = requireView().findViewById(R.id.btn_reset_settings)

        // 保存方式监听
        saveModeGroup.setOnCheckedChangeListener { _, checkedId ->
            when (checkedId) {
                R.id.radio_fusion_mode -> saveMeasurementMode("fusion")
                R.id.radio_layer_mode -> saveMeasurementMode("layer")
            }
        }

        // 测量数据监听
        val measurementListener = CompoundButton.OnCheckedChangeListener { _, _ ->
            saveMeasurementSettings()
        }
        measurementMarksCheck.setOnCheckedChangeListener(measurementListener)
        measurementValuesCheck.setOnCheckedChangeListener(measurementListener)
        measurementReportCheck.setOnCheckedChangeListener(measurementListener)

        // 标注样式监听
        labelStyleSpinner.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                saveLabelStyle(position)
            }
            override fun onNothingSelected(parent: AdapterView<*>?) {}
        }

        // 字体大小监听
        fontSizeSpinner.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                saveFontSize(position)
            }
            override fun onNothingSelected(parent: AdapterView<*>?) {}
        }

        // 按钮监听
        stylePreviewButton.setOnClickListener {
            // 样式预览逻辑
            Toast.makeText(requireContext(), "测量样式预览中...", Toast.LENGTH_SHORT).show()
        }

        resetSettingsButton.setOnClickListener {
            // 重置设置逻辑
            resetMeasurementSettings()
            Toast.makeText(requireContext(), "测量设置已重置", Toast.LENGTH_SHORT).show()
        }

        // 加载保存的设置
        loadMeasurementSettings()
    }

    private fun loadMeasurementSettings() {
        val prefs = requireContext().getSharedPreferences("CameraSettings", Context.MODE_PRIVATE)

        // 加载保存方式
        val mode = prefs.getString("measurement_mode", "fusion")
        saveModeGroup.check(when (mode) {
            "fusion" -> R.id.radio_fusion_mode
            else -> R.id.radio_layer_mode
        })

        // 加载测量数据设置
        measurementMarksCheck.isChecked = prefs.getBoolean("save_measurement_marks", true)
        measurementValuesCheck.isChecked = prefs.getBoolean("save_measurement_values", true)
        measurementReportCheck.isChecked = prefs.getBoolean("save_measurement_report", false)

        // 加载标注样式
        val labelStyle = prefs.getInt("label_style", 0)
        labelStyleSpinner.setSelection(labelStyle)

        // 加载字体大小
        val fontSize = prefs.getInt("font_size", 1) // 默认选中中号
        fontSizeSpinner.setSelection(fontSize)
    }

    private fun saveMeasurementMode(mode: String) {
        val prefs = requireContext().getSharedPreferences("CameraSettings", Context.MODE_PRIVATE)
        prefs.edit().putString("measurement_mode", mode).apply()
    }

    private fun saveMeasurementSettings() {
        val prefs = requireContext().getSharedPreferences("CameraSettings", Context.MODE_PRIVATE).edit()
        prefs.putBoolean("save_measurement_marks", measurementMarksCheck.isChecked)
        prefs.putBoolean("save_measurement_values", measurementValuesCheck.isChecked)
        prefs.putBoolean("save_measurement_report", measurementReportCheck.isChecked)
        prefs.apply()
    }

    private fun saveLabelStyle(position: Int) {
        val prefs = requireContext().getSharedPreferences("CameraSettings", Context.MODE_PRIVATE)
        prefs.edit().putInt("label_style", position).apply()
    }

    private fun saveFontSize(position: Int) {
        val prefs = requireContext().getSharedPreferences("CameraSettings", Context.MODE_PRIVATE)
        prefs.edit().putInt("font_size", position).apply()
    }

    private fun resetMeasurementSettings() {
        // 重置为默认值
        saveModeGroup.check(R.id.radio_fusion_mode)
        measurementMarksCheck.isChecked = true
        measurementValuesCheck.isChecked = true
        measurementReportCheck.isChecked = false
        labelStyleSpinner.setSelection(0)
        fontSizeSpinner.setSelection(1) // 中号字体

        // 保存默认设置
        val prefs = requireContext().getSharedPreferences("CameraSettings", Context.MODE_PRIVATE).edit()
        prefs.putString("measurement_mode", "fusion")
        prefs.putBoolean("save_measurement_marks", true)
        prefs.putBoolean("save_measurement_values", true)
        prefs.putBoolean("save_measurement_report", false)
        prefs.putInt("label_style", 0)
        prefs.putInt("font_size", 1)
        prefs.apply()
    }

    private fun setupAdvancedSettings() {
        // 初始化控件
        colorSpaceGroup = requireView().findViewById(R.id.color_space_group)
        colorDepthGroup = requireView().findViewById(R.id.color_depth_group)
        compressionSpinner = requireView().findViewById(R.id.spinner_compression)
        fusionCheck = requireView().findViewById(R.id.check_fusion_mode)
        layerCheck = requireView().findViewById(R.id.check_layer_mode)

        // 加载保存的设置
        loadAdvancedSettings()

        // 事件监听
        colorSpaceGroup.setOnCheckedChangeListener { _, checkedId ->
            saveColorSpace(if (checkedId == R.id.radio_srgb) "sRGB" else "Adobe RGB")
        }

        colorDepthGroup.setOnCheckedChangeListener { _, checkedId ->
            saveColorDepth(if (checkedId == R.id.radio_8bit) "8bit" else "16bit")
        }

        compressionSpinner.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: AdapterView<*>?, view: View?, pos: Int, id: Long) {
                saveCompressionLevel(pos)
            }
            override fun onNothingSelected(parent: AdapterView<*>?) {}
        }

        // 后期功能保存
        val postProcessListener = CompoundButton.OnCheckedChangeListener { _, _ ->
            savePostProcessSettings()
        }
        fusionCheck.setOnCheckedChangeListener(postProcessListener)
        layerCheck.setOnCheckedChangeListener(postProcessListener)

        // 按钮事件
        requireView().findViewById<Button>(R.id.btn_param_details).setOnClickListener {
            showAdvancedParamDetails()
        }
        requireView().findViewById<Button>(R.id.btn_compatibility_test).setOnClickListener {
            runCompatibilityTest()
        }
    }

    private fun loadAdvancedSettings() {
        val prefs = requireContext().getSharedPreferences("CameraSettings", Context.MODE_PRIVATE)

        // 色彩空间
        colorSpaceGroup.check(
            if (prefs.getString("color_space", "sRGB") == "sRGB")
                R.id.radio_srgb else R.id.radio_adobe_rgb
        )

        // 色彩深度
        colorDepthGroup.check(
            if (prefs.getString("color_depth", "8bit") == "8bit")
                R.id.radio_8bit else R.id.radio_16bit
        )

        // 压缩质量
        compressionSpinner.setSelection(prefs.getInt("compression_level", 2)) // 默认标准

        // 后期功能
        fusionCheck.isChecked = prefs.getBoolean("fusion_mode", false)
        layerCheck.isChecked = prefs.getBoolean("layer_mode", false)
    }

    private fun saveColorSpace(space: String) {
    }

    private fun saveColorDepth(depth: String) {
    }

    private fun saveCompressionLevel(level: Int) {
    }

    private fun savePostProcessSettings() {
    }

    private fun showAdvancedParamDetails() {
        // 显示参数说明弹窗
        AlertDialog.Builder(requireContext())
            .setTitle("高级参数说明")
            .setMessage("sRGB：通用色彩标准\nAdobe RGB：更广色域\n16位深度：支持68亿色彩\n无损压缩：保留全部图像数据")
            .setPositiveButton("确定", null)
            .show()
    }

    private fun runCompatibilityTest() {
        // 执行兼容性测试
        Toast.makeText(requireContext(), "正在检测设备兼容性...", Toast.LENGTH_SHORT).show()
    }
}