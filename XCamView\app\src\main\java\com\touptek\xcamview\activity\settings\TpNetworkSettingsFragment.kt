package com.touptek.xcamview.activity.settings

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import com.touptek.xcamview.R

class TpNetworkSettingsFragment : Fragment() {

    companion object {
        private const val TAG = "NetworkSettings"
    }

    // 声明所有视图组件
    private lateinit var tvEthernetStatus: TextView
    private lateinit var tvEthernetIp: TextView
    private lateinit var btnEthernetConfig: Button
    private lateinit var btnDisconnectEthernet: Button

    private lateinit var tvSTAStatus: TextView
    private lateinit var tvCurrentWifi: TextView
    private lateinit var btnToggleWifi: Button

    private lateinit var tvApStatus: TextView
    private lateinit var tvApSsid: TextView
    private lateinit var btnToggleAp: Button

    private lateinit var tvRtspStatus: TextView
    private lateinit var tvRtspUri: TextView
    private lateinit var spinnerNetworkInterface: Spinner
    private lateinit var rgStreamType: RadioGroup
    private lateinit var btnStartStream: Button
    private lateinit var btnStopStream: Button

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_network_settings, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // 1. 初始化所有视图组件 [1,5](@ref)
        initViews(view)

        // 2. 设置有线网络区域
        setupEthernetSection()

        // 3. 设置无线网络区域
        setupWifiSection()

        // 4. 设置RTSP推流区域
        setupRtspSection()
    }

    private fun initViews(view: View) {
        // 有线网络组件
        tvEthernetStatus = view.findViewById(R.id.tvEthernetStatus)
        tvEthernetIp = view.findViewById(R.id.tvEthernetIp)
        btnEthernetConfig = view.findViewById(R.id.btnEthernetConfig)
        btnDisconnectEthernet = view.findViewById(R.id.btnDisconnectEthernet)

        // 无线网络组件
        tvSTAStatus = view.findViewById(R.id.tvSTAStatus)
        tvCurrentWifi = view.findViewById(R.id.tvCurrentWifi)
        btnToggleWifi = view.findViewById(R.id.btnToggleWifi)
        tvApStatus = view.findViewById(R.id.tvApStatus)
        tvApSsid = view.findViewById(R.id.tvApSsid)
        btnToggleAp = view.findViewById(R.id.btnToggleAp)

        // RTSP推流组件
        tvRtspStatus = view.findViewById(R.id.tvRtspStatus)
        tvRtspUri = view.findViewById(R.id.tvRtspUri)
        spinnerNetworkInterface = view.findViewById(R.id.spinnerNetworkInterface)
        rgStreamType = view.findViewById(R.id.rgStreamType)
        btnStartStream = view.findViewById(R.id.btnStartStream)
        btnStopStream = view.findViewById(R.id.btnStopStream)
    }

    private fun setupEthernetSection() {
        // 设置初始状态文本颜色
        tvEthernetStatus.setTextColor(ContextCompat.getColor(requireContext(), R.color.red))

        // 设置按钮点击事件 [13,14](@ref)
        btnEthernetConfig.setOnClickListener {
            // 处理网络配置逻辑
            Toast.makeText(context, "打开网络配置", Toast.LENGTH_SHORT).show()
        }

        btnDisconnectEthernet.setOnClickListener {
            // 处理断开连接逻辑
            updateEthernetStatus("已断开", "0.0.0.0")
            btnDisconnectEthernet.isEnabled = false
        }
    }

    private fun updateEthernetStatus(status: String, ip: String) {
        tvEthernetStatus.text = status
        tvEthernetIp.text = ip

        // 根据状态更新文本颜色 [6,7](@ref)
        val colorRes = if (status == "已连接") R.color.blue_500 else R.color.red
        tvEthernetStatus.setTextColor(ContextCompat.getColor(requireContext(), colorRes))
    }

    private fun setupWifiSection() {
        // 设置初始状态
        updateWifiStatus("未连接", "无")
        updateApStatus("已关闭", "MyHotspot")

        // STA模式按钮事件
        btnToggleWifi.setOnClickListener {
            val isConnected = tvSTAStatus.text == "已连接"
            if (isConnected) {
                updateWifiStatus("未连接", "无")
                btnToggleWifi.text = "连接WiFi"
            } else {
                updateWifiStatus("已连接", "MyWiFi_Network")
                btnToggleWifi.text = "断开连接"
            }
        }

        // AP模式按钮事件
        btnToggleAp.setOnClickListener {
            val isApOn = tvApStatus.text == "已开启"
            if (isApOn) {
                updateApStatus("已关闭", "MyHotspot")
                btnToggleAp.text = "开启热点"
            } else {
                updateApStatus("已开启", "MyHotspot")
                btnToggleAp.text = "关闭热点"
            }
        }
    }

    private fun updateWifiStatus(status: String, network: String) {
        tvSTAStatus.text = status
        tvCurrentWifi.text = network

        // 更新文本颜色 [8](@ref)
        val colorRes = if (status == "已连接") R.color.blue_500 else R.color.red
        tvSTAStatus.setTextColor(ContextCompat.getColor(requireContext(), colorRes))
    }

    private fun updateApStatus(status: String, ssid: String) {
        tvApStatus.text = status
        tvApSsid.text = ssid

//        val colorRes = if (status == "已开启") R.color.green else R.color.red
//        tvApStatus.setTextColor(ContextCompat.getColor(requireContext(), colorRes))
    }

    private fun setupRtspSection() {
        // 设置初始状态
        tvRtspStatus.setTextColor(ContextCompat.getColor(requireContext(), R.color.red))

        // 初始化网络接口下拉列表
        val interfaces = arrayOf("以太网", "WiFi", "移动网络")
        val adapter =
            ArrayAdapter(requireContext(), android.R.layout.simple_spinner_item, interfaces)
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        spinnerNetworkInterface.adapter = adapter

        // 设置流类型选择
        rgStreamType.setOnCheckedChangeListener { _, checkedId ->
            when (checkedId) {
                R.id.rbCameraStream -> { /* 处理摄像头流选择 */ }
                R.id.rbScreenStream -> { /* 处理屏幕流选择 */ }
            }
        }

        // 设置按钮事件
        btnStartStream.setOnClickListener {
            tvRtspStatus.text = "推流中"
//            tvRtspStatus.setTextColor(ContextCompat.getColor(requireContext(), R.color.green))
            btnStartStream.isEnabled = false
            btnStopStream.isEnabled = true
        }

        btnStopStream.setOnClickListener {
            tvRtspStatus.text = "已停止"
            tvRtspStatus.setTextColor(ContextCompat.getColor(requireContext(), R.color.red))
            btnStartStream.isEnabled = true
            btnStopStream.isEnabled = false
        }

    }
}