<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="org.opencv.test" >

    <uses-sdk
        android:minSdkVersion="24"
        android:targetSdkVersion="35" />

    <instrumentation
        android:name="android.test.InstrumentationTestRunner"
        android:functionalTest="false"
        android:handleProfiling="false"
        android:label="Tests for org.opencv.test"
        android:targetPackage="org.opencv.test" />

    <application
        android:debuggable="true"
        android:extractNativeLibs="false" >
        <uses-library android:name="android.test.runner" />
    </application>

</manifest>