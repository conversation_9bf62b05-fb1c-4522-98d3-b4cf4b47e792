<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_392) on Sun Jun 02 16:58:27 UTC 2024 -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>UsacParams (OpenCV 4.10.0 Java documentation)</title>
<meta name="date" content="2024-06-02">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="UsacParams (OpenCV 4.10.0 Java documentation)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/calib3d/StereoSGBM.html" title="class in org.opencv.calib3d"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/calib3d/UsacParams.html" target="_top">Frames</a></li>
<li><a href="UsacParams.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.opencv.calib3d</div>
<h2 title="Class UsacParams" class="title">Class UsacParams</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li>java.lang.Object</li>
<li>
<ul class="inheritance">
<li>org.opencv.calib3d.UsacParams</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">UsacParams</span>
extends java.lang.Object</pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/UsacParams.html#UsacParams--">UsacParams</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/opencv/calib3d/UsacParams.html" title="class in org.opencv.calib3d">UsacParams</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/UsacParams.html#Z:Z__fromPtr__-long-">__fromPtr__</a></span>(long&nbsp;addr)</code>&nbsp;</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/UsacParams.html#get_confidence--">get_confidence</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/UsacParams.html#get_final_polisher_iterations--">get_final_polisher_iterations</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/UsacParams.html#get_final_polisher--">get_final_polisher</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/UsacParams.html#get_isParallel--">get_isParallel</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/UsacParams.html#get_loIterations--">get_loIterations</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/UsacParams.html#get_loMethod--">get_loMethod</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/UsacParams.html#get_loSampleSize--">get_loSampleSize</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/UsacParams.html#get_maxIterations--">get_maxIterations</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/UsacParams.html#get_neighborsSearch--">get_neighborsSearch</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/UsacParams.html#get_randomGeneratorState--">get_randomGeneratorState</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/UsacParams.html#get_sampler--">get_sampler</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/UsacParams.html#get_score--">get_score</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/UsacParams.html#get_threshold--">get_threshold</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/UsacParams.html#getNativeObjAddr--">getNativeObjAddr</a></span>()</code>&nbsp;</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/UsacParams.html#set_confidence-double-">set_confidence</a></span>(double&nbsp;confidence)</code>&nbsp;</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/UsacParams.html#set_final_polisher_iterations-int-">set_final_polisher_iterations</a></span>(int&nbsp;final_polisher_iterations)</code>&nbsp;</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/UsacParams.html#set_final_polisher-int-">set_final_polisher</a></span>(int&nbsp;final_polisher)</code>&nbsp;</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/UsacParams.html#set_isParallel-boolean-">set_isParallel</a></span>(boolean&nbsp;isParallel)</code>&nbsp;</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/UsacParams.html#set_loIterations-int-">set_loIterations</a></span>(int&nbsp;loIterations)</code>&nbsp;</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/UsacParams.html#set_loMethod-int-">set_loMethod</a></span>(int&nbsp;loMethod)</code>&nbsp;</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/UsacParams.html#set_loSampleSize-int-">set_loSampleSize</a></span>(int&nbsp;loSampleSize)</code>&nbsp;</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/UsacParams.html#set_maxIterations-int-">set_maxIterations</a></span>(int&nbsp;maxIterations)</code>&nbsp;</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/UsacParams.html#set_neighborsSearch-int-">set_neighborsSearch</a></span>(int&nbsp;neighborsSearch)</code>&nbsp;</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/UsacParams.html#set_randomGeneratorState-int-">set_randomGeneratorState</a></span>(int&nbsp;randomGeneratorState)</code>&nbsp;</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/UsacParams.html#set_sampler-int-">set_sampler</a></span>(int&nbsp;sampler)</code>&nbsp;</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/UsacParams.html#set_score-int-">set_score</a></span>(int&nbsp;score)</code>&nbsp;</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/opencv/calib3d/UsacParams.html#set_threshold-double-">set_threshold</a></span>(double&nbsp;threshold)</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.Object</h3>
<code>equals, getClass, hashCode, notify, notifyAll, toString, wait, wait, wait</code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="UsacParams--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>UsacParams</h4>
<pre>public&nbsp;UsacParams()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="Z:Z__fromPtr__-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>__fromPtr__</h4>
<pre>public static&nbsp;<a href="../../../org/opencv/calib3d/UsacParams.html" title="class in org.opencv.calib3d">UsacParams</a>&nbsp;__fromPtr__(long&nbsp;addr)</pre>
</li>
</ul>
<a name="get_confidence--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_confidence</h4>
<pre>public&nbsp;double&nbsp;get_confidence()</pre>
</li>
</ul>
<a name="get_final_polisher_iterations--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_final_polisher_iterations</h4>
<pre>public&nbsp;int&nbsp;get_final_polisher_iterations()</pre>
</li>
</ul>
<a name="get_final_polisher--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_final_polisher</h4>
<pre>public&nbsp;int&nbsp;get_final_polisher()</pre>
</li>
</ul>
<a name="get_isParallel--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_isParallel</h4>
<pre>public&nbsp;boolean&nbsp;get_isParallel()</pre>
</li>
</ul>
<a name="get_loIterations--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_loIterations</h4>
<pre>public&nbsp;int&nbsp;get_loIterations()</pre>
</li>
</ul>
<a name="get_loMethod--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_loMethod</h4>
<pre>public&nbsp;int&nbsp;get_loMethod()</pre>
</li>
</ul>
<a name="get_loSampleSize--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_loSampleSize</h4>
<pre>public&nbsp;int&nbsp;get_loSampleSize()</pre>
</li>
</ul>
<a name="get_maxIterations--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_maxIterations</h4>
<pre>public&nbsp;int&nbsp;get_maxIterations()</pre>
</li>
</ul>
<a name="get_neighborsSearch--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_neighborsSearch</h4>
<pre>public&nbsp;int&nbsp;get_neighborsSearch()</pre>
</li>
</ul>
<a name="get_randomGeneratorState--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_randomGeneratorState</h4>
<pre>public&nbsp;int&nbsp;get_randomGeneratorState()</pre>
</li>
</ul>
<a name="get_sampler--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_sampler</h4>
<pre>public&nbsp;int&nbsp;get_sampler()</pre>
</li>
</ul>
<a name="get_score--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_score</h4>
<pre>public&nbsp;int&nbsp;get_score()</pre>
</li>
</ul>
<a name="get_threshold--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get_threshold</h4>
<pre>public&nbsp;double&nbsp;get_threshold()</pre>
</li>
</ul>
<a name="getNativeObjAddr--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNativeObjAddr</h4>
<pre>public&nbsp;long&nbsp;getNativeObjAddr()</pre>
</li>
</ul>
<a name="set_confidence-double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_confidence</h4>
<pre>public&nbsp;void&nbsp;set_confidence(double&nbsp;confidence)</pre>
</li>
</ul>
<a name="set_final_polisher_iterations-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_final_polisher_iterations</h4>
<pre>public&nbsp;void&nbsp;set_final_polisher_iterations(int&nbsp;final_polisher_iterations)</pre>
</li>
</ul>
<a name="set_final_polisher-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_final_polisher</h4>
<pre>public&nbsp;void&nbsp;set_final_polisher(int&nbsp;final_polisher)</pre>
</li>
</ul>
<a name="set_isParallel-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_isParallel</h4>
<pre>public&nbsp;void&nbsp;set_isParallel(boolean&nbsp;isParallel)</pre>
</li>
</ul>
<a name="set_loIterations-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_loIterations</h4>
<pre>public&nbsp;void&nbsp;set_loIterations(int&nbsp;loIterations)</pre>
</li>
</ul>
<a name="set_loMethod-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_loMethod</h4>
<pre>public&nbsp;void&nbsp;set_loMethod(int&nbsp;loMethod)</pre>
</li>
</ul>
<a name="set_loSampleSize-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_loSampleSize</h4>
<pre>public&nbsp;void&nbsp;set_loSampleSize(int&nbsp;loSampleSize)</pre>
</li>
</ul>
<a name="set_maxIterations-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_maxIterations</h4>
<pre>public&nbsp;void&nbsp;set_maxIterations(int&nbsp;maxIterations)</pre>
</li>
</ul>
<a name="set_neighborsSearch-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_neighborsSearch</h4>
<pre>public&nbsp;void&nbsp;set_neighborsSearch(int&nbsp;neighborsSearch)</pre>
</li>
</ul>
<a name="set_randomGeneratorState-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_randomGeneratorState</h4>
<pre>public&nbsp;void&nbsp;set_randomGeneratorState(int&nbsp;randomGeneratorState)</pre>
</li>
</ul>
<a name="set_sampler-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_sampler</h4>
<pre>public&nbsp;void&nbsp;set_sampler(int&nbsp;sampler)</pre>
</li>
</ul>
<a name="set_score-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set_score</h4>
<pre>public&nbsp;void&nbsp;set_score(int&nbsp;score)</pre>
</li>
</ul>
<a name="set_threshold-double-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>set_threshold</h4>
<pre>public&nbsp;void&nbsp;set_threshold(double&nbsp;threshold)</pre>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
<div class="aboutLanguage">
            <script>
              var url = window.location.href;
              var pos = url.lastIndexOf('/javadoc/');
              url = pos >= 0 ? (url.substring(0, pos) + '/javadoc/mymath.js') : (window.location.origin + '/mymath.js');
              var script = document.createElement('script');
              script.src = 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML,' + url;
              document.getElementsByTagName('head')[0].appendChild(script);
            </script>
</div>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/opencv/calib3d/StereoSGBM.html" title="class in org.opencv.calib3d"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/opencv/calib3d/UsacParams.html" target="_top">Frames</a></li>
<li><a href="UsacParams.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Generated on 2024-06-02 16:58:23 / OpenCV 4.10.0</small></p>
</body>
</html>
