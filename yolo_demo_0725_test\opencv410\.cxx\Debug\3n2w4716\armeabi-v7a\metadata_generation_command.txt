                        -HE:\work\Andriod\yolo_demo\opencv410\libcxx_helper
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=24
-DANDROID_PLATFORM=android-24
-DANDROID_ABI=armeabi-v7a
-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a
-DANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\26.1.10909125
-DCMAKE_ANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\26.1.10909125
-DC<PERSON>KE_TOOLCHAIN_FILE=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\26.1.10909125\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=E:\work\Andriod\yolo_demo\opencv410\build\intermediates\cxx\Debug\3n2w4716\obj\armeabi-v7a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=E:\work\Andriod\yolo_demo\opencv410\build\intermediates\cxx\Debug\3n2w4716\obj\armeabi-v7a
-DCMAKE_BUILD_TYPE=Debug
-BE:\work\Andriod\yolo_demo\opencv410\.cxx\Debug\3n2w4716\armeabi-v7a
-GNinja
-DANDROID_STL=c++_shared
                        Build command args: []
                        Version: 2